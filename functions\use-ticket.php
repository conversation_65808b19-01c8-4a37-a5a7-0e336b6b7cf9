<?php
session_start();
include('server.php');

header('Content-Type: application/json');

if (isset($_POST['username']) && !empty($_POST['username'])) {
    $username = mysqli_real_escape_string($conn, $_POST['username']);

    $checkSql = "SELECT remaining_tickets
                 FROM purchasetickets
                 WHERE username = '$username'
                 AND ticket_type = 'STARTER'
                 AND remaining_tickets > 0
                 AND purchase_time >= CURDATE() - INTERVAL 1 YEAR
                 ORDER BY purchase_time ASC
                 LIMIT 1";

    $result = mysqli_query($conn, $checkSql);

    if (mysqli_num_rows($result) > 0) {
        $sql = "UPDATE purchasetickets
                SET remaining_tickets = remaining_tickets - 1
                WHERE username = '$username'
                AND ticket_type = 'STARTER'
                AND remaining_tickets > 0
                AND purchase_time >= CURDATE() - INTERVAL 1 YEAR
                ORDER BY purchase_time ASC
                LIMIT 1";

        if (mysqli_query($conn, $sql)) {
            echo json_encode(['status' => 'success', 'message' => 'Ticket used successfully!']);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Error updating ticket: ' . mysqli_error($conn)]);
        }
    } else {
        echo json_encode(['status' => 'error', 'message' => 'No tickets available. Please purchase more tickets.']);
    }
} else {
    echo json_encode(['status' => 'error', 'message' => 'No username provided.']);
}
?>
