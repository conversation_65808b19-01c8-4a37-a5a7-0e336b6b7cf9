<?php
// Auto-detect environment for file paths
$is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
$file_base_path = $is_localhost ? '/helloit' : '';

require_once $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/vendor/autoload.php';
include($_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/functions/server.php');
session_start();

// Auto-detect environment for URL paths
$is_localhost_url = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
$base_path = $is_localhost_url ? '/helloit' : '';
$url_base = $is_localhost_url ? '/helloit' : '';

echo "<h2>Payment Methods Debug Tool</h2>";

// Check session
echo "<h3>Session Information:</h3>";
echo "<p><strong>Session ID:</strong> " . session_id() . "</p>";
echo "<p><strong>Username:</strong> " . ($_SESSION['username'] ?? 'Not set') . "</p>";
echo "<p><strong>User ID:</strong> " . ($_SESSION['user_id'] ?? 'Not set') . "</p>";

if (isset($_SESSION['username'])) {
    $username = $_SESSION['username'];
    
    // Check user in database
    echo "<h3>Database User Information:</h3>";
    $user_query = "SELECT id, username, email, stripe_customer_id FROM user WHERE username = '$username'";
    $user_result = mysqli_query($conn, $user_query);
    
    if ($user_result && mysqli_num_rows($user_result) > 0) {
        $user = mysqli_fetch_assoc($user_result);
        echo "<p><strong>User ID:</strong> " . $user['id'] . "</p>";
        echo "<p><strong>Username:</strong> " . $user['username'] . "</p>";
        echo "<p><strong>Email:</strong> " . $user['email'] . "</p>";
        echo "<p><strong>Stripe Customer ID:</strong> " . ($user['stripe_customer_id'] ?? 'Not set') . "</p>";
        
        // Check payment methods in database
        echo "<h3>Payment Methods in Database:</h3>";
        $payment_methods_query = "SELECT * FROM payment_methods WHERE user_id = " . $user['id'] . " ORDER BY is_default DESC, created_at DESC";
        $payment_methods_result = mysqli_query($conn, $payment_methods_query);
        
        if ($payment_methods_result && mysqli_num_rows($payment_methods_result) > 0) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>Payment Method ID</th><th>Brand</th><th>Last 4</th><th>Exp</th><th>Default</th><th>Created</th></tr>";
            
            while ($method = mysqli_fetch_assoc($payment_methods_result)) {
                echo "<tr>";
                echo "<td>" . $method['id'] . "</td>";
                echo "<td>" . $method['payment_method_id'] . "</td>";
                echo "<td>" . $method['card_brand'] . "</td>";
                echo "<td>" . $method['card_last4'] . "</td>";
                echo "<td>" . $method['card_exp_month'] . "/" . $method['card_exp_year'] . "</td>";
                echo "<td>" . ($method['is_default'] ? 'Yes' : 'No') . "</td>";
                echo "<td>" . $method['created_at'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p><strong>No payment methods found in database</strong></p>";
        }
        
        // Test the API directly
        echo "<h3>API Test:</h3>";
        echo "<button onclick='testAPI()'>Test Payment Methods API</button>";
        echo "<div id='api-result'></div>";
        
    } else {
        echo "<p><strong>User not found in database</strong></p>";
    }
} else {
    echo "<p><strong>User not logged in</strong></p>";
    echo "<p><a href='sign-in.php'>Login first</a></p>";
}
?>

<script>
function testAPI() {
    const resultDiv = document.getElementById('api-result');
    resultDiv.innerHTML = '<p>Testing API...</p>';
    
    fetch('<?php echo $base_path; ?>/functions/get-payment-methods.php')
        .then(response => response.text())
        .then(text => {
            console.log('Raw API response:', text);
            try {
                const data = JSON.parse(text);
                resultDiv.innerHTML = '<h4>API Response:</h4><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (e) {
                resultDiv.innerHTML = '<h4>API Response (Raw):</h4><pre>' + text + '</pre>';
            }
        })
        .catch(error => {
            resultDiv.innerHTML = '<h4>API Error:</h4><pre>' + error + '</pre>';
        });
}
</script>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
button { background: #007bff; color: white; padding: 10px 15px; border: none; border-radius: 4px; cursor: pointer; }
pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; }
</style>
