<?php
session_start();

// ตรวจสอบว่ามีการส่งค่า cart_item_id และ quantity มาหรือไม่
if (isset($_POST['cart_item_id']) && isset($_POST['quantity'])) {
    $cart_item_id = $_POST['cart_item_id'];
    $quantity = $_POST['quantity'];

    // ตรวจสอบว่า $_SESSION["cart_item"] มีข้อมูลหรือไม่
    if (!empty($_SESSION["cart_item"])) {
        // ลูปผ่านรายการสินค้าทั้งหมดในตะกร้า
        foreach ($_SESSION["cart_item"] as $key => $item) {
            if ($item['ticketid'] == $cart_item_id) {
                // อัปเดตจำนวนสินค้า
                $_SESSION["cart_item"][$key]['quantity'] = $quantity;

                // คำนวณราคาใหม่
                $_SESSION["cart_item"][$key]['price'] = $item['dollar_price_per_package'] * $quantity;

                echo "Quantity updated successfully.";
                exit;
            }
        }
    }

    echo "Item not found in cart.";
} else {
    echo "Invalid request.";
}
