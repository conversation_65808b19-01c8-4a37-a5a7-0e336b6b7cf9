<?php
session_start();
include($_SERVER['DOCUMENT_ROOT'] . '/helloit/functions/server.php');

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['status' => 'error', 'message' => 'Invalid request method']);
    exit;
}

$cart_item_id = isset($_POST['cart_item_id']) ? trim($_POST['cart_item_id']) : '';
$quantity = isset($_POST['quantity']) ? intval($_POST['quantity']) : 0;

if (empty($cart_item_id) || $quantity < 1) {
    echo json_encode(['status' => 'error', 'message' => 'Invalid cart item ID or quantity']);
    exit;
}

$user_id = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;

try {
    if ($user_id) {
        // Handle logged-in user cart
        $stmt = $conn->prepare("UPDATE cart_items SET quantity = ? WHERE id = ? AND cart_id IN (SELECT cart_id FROM cart WHERE user_id = ? AND status = 'active')");
        $stmt->bind_param("iii", $quantity, $cart_item_id, $user_id);
        
        if ($stmt->execute() && $stmt->affected_rows > 0) {
            echo json_encode(['status' => 'success', 'message' => 'Quantity updated successfully']);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'Failed to update quantity or item not found']);
        }
        $stmt->close();
    } else {
        // Handle guest cart (session-based)
        if (isset($_SESSION['guest_cart']) && is_array($_SESSION['guest_cart'])) {
            $updated = false;
            foreach ($_SESSION['guest_cart'] as $key => $item) {
                if (isset($item['cart_item_id']) && $item['cart_item_id'] === $cart_item_id) {
                    $_SESSION['guest_cart'][$key]['quantity'] = $quantity;
                    $updated = true;
                    break;
                }
            }
            
            if ($updated) {
                echo json_encode(['status' => 'success', 'message' => 'Quantity updated successfully']);
            } else {
                echo json_encode(['status' => 'error', 'message' => 'Item not found in cart']);
            }
        } else {
            echo json_encode(['status' => 'error', 'message' => 'No cart found']);
        }
    }
} catch (Exception $e) {
    error_log("Cart update quantity error: " . $e->getMessage());
    echo json_encode(['status' => 'error', 'message' => 'An error occurred while updating quantity']);
}
?>
