<?php
// Auto-detect environment for file paths
$is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
$file_base_path = $is_localhost ? '/helloit' : '';

require_once $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/vendor/autoload.php';
include($_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/functions/server.php');
session_start();

// Auto-detect environment for URL paths
$is_localhost_url = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
$base_path = $is_localhost_url ? '/helloit' : '';

echo "<h2>Cart Debug Tool</h2>";

// Check session
echo "<h3>Session Information:</h3>";
echo "<p><strong>Session ID:</strong> " . session_id() . "</p>";
echo "<p><strong>Username:</strong> " . ($_SESSION['username'] ?? 'Not set') . "</p>";
echo "<p><strong>User ID:</strong> " . ($_SESSION['user_id'] ?? 'Not set') . "</p>";

if (isset($_SESSION['user_id'])) {
    $user_id = $_SESSION['user_id'];
    
    echo "<h3>Cart Information:</h3>";
    
    // Check cart table
    echo "<h4>Cart Table:</h4>";
    $cart_query = "SELECT * FROM cart WHERE user_id = ?";
    $cart_stmt = $conn->prepare($cart_query);
    $cart_stmt->bind_param("i", $user_id);
    $cart_stmt->execute();
    $cart_result = $cart_stmt->get_result();
    
    if ($cart_result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Cart ID</th><th>User ID</th><th>Status</th><th>Created At</th></tr>";
        
        while ($cart = $cart_result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $cart['cart_id'] . "</td>";
            echo "<td>" . $cart['user_id'] . "</td>";
            echo "<td>" . $cart['status'] . "</td>";
            echo "<td>" . $cart['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p><strong>No cart found for user</strong></p>";
    }
    
    // Check cart items using pre-checkout query
    echo "<h4>Cart Items (Pre-checkout Query):</h4>";
    $query = "SELECT ci.ticket_id, ci.quantity, t.ticket_type, t.dollar_price_per_package, t.package_size, t.numbers_per_package
              FROM cart_items ci
              JOIN tickets t ON ci.ticket_id = t.ticketid
              WHERE ci.cart_id IN (SELECT cart_id FROM cart WHERE user_id = ? AND status = 'active')";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $cart_items = $result->fetch_all(MYSQLI_ASSOC);
    
    if (!empty($cart_items)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Ticket ID</th><th>Type</th><th>Package Size</th><th>Price</th><th>Quantity</th><th>Total</th></tr>";
        
        $total = 0;
        foreach ($cart_items as $item) {
            $item_total = $item['quantity'] * $item['dollar_price_per_package'];
            $total += $item_total;
            
            echo "<tr>";
            echo "<td>" . $item['ticket_id'] . "</td>";
            echo "<td>" . $item['ticket_type'] . "</td>";
            echo "<td>" . $item['package_size'] . "</td>";
            echo "<td>$" . number_format($item['dollar_price_per_package'], 2) . "</td>";
            echo "<td>" . $item['quantity'] . "</td>";
            echo "<td>$" . number_format($item_total, 2) . "</td>";
            echo "</tr>";
        }
        echo "<tr style='background-color: #f0f0f0; font-weight: bold;'>";
        echo "<td colspan='5'>TOTAL</td>";
        echo "<td>$" . number_format($total, 2) . "</td>";
        echo "</tr>";
        echo "</table>";
    } else {
        echo "<p><strong>No cart items found</strong></p>";
    }
    
    // Check cart items using cart.php query
    echo "<h4>Cart Items (Cart.php Query):</h4>";
    $query2 = "SELECT c.cart_id, ci.id AS cart_item_id, ci.ticket_id, ci.quantity, t.ticket_type, t.dollar_price_per_package, t.package_size, t.numbers_per_package
              FROM cart c
              JOIN cart_items ci ON c.cart_id = ci.cart_id
              JOIN tickets t ON ci.ticket_id = t.ticketid
              WHERE c.user_id = ? AND c.status = 'active'";
    $stmt2 = $conn->prepare($query2);
    $stmt2->bind_param("i", $user_id);
    $stmt2->execute();
    $result2 = $stmt2->get_result();
    $cart_items2 = $result2->fetch_all(MYSQLI_ASSOC);
    
    if (!empty($cart_items2)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Cart ID</th><th>Item ID</th><th>Ticket ID</th><th>Type</th><th>Package Size</th><th>Price</th><th>Quantity</th></tr>";
        
        foreach ($cart_items2 as $item) {
            echo "<tr>";
            echo "<td>" . $item['cart_id'] . "</td>";
            echo "<td>" . $item['cart_item_id'] . "</td>";
            echo "<td>" . $item['ticket_id'] . "</td>";
            echo "<td>" . $item['ticket_type'] . "</td>";
            echo "<td>" . $item['package_size'] . "</td>";
            echo "<td>$" . number_format($item['dollar_price_per_package'], 2) . "</td>";
            echo "<td>" . $item['quantity'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p><strong>No cart items found with cart.php query</strong></p>";
    }
    
} else {
    echo "<p><strong>User not logged in</strong></p>";
    echo "<p><a href='sign-in.php'>Login first</a></p>";
}

// Check guest cart
echo "<h3>Guest Cart (Session):</h3>";
if (isset($_SESSION['guest_cart']) && !empty($_SESSION['guest_cart'])) {
    echo "<pre>" . print_r($_SESSION['guest_cart'], true) . "</pre>";
} else {
    echo "<p><strong>No guest cart in session</strong></p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; }
</style>
