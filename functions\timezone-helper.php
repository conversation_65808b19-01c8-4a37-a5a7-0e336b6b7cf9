<?php
/**
 * PHP UTC Helper - Force GMT+0 without database SUPER privileges
 * Works regardless of server timezone configuration (MST, GMT+7, etc.)
 */

// Prevent multiple inclusions
if (defined('UTC_TIME_HELPER_LOADED')) {
    return;
}
define('UTC_TIME_HELPER_LOADED', true);

// Additional protection against class redeclaration
if (!class_exists('UTCTimeHelper')) {

class UTCTimeHelper {
    
    // Default user timezone
    const DEFAULT_TIMEZONE = 'Asia/Singapore';
    
    /**
     * Get current UTC time - ALWAYS correct regardless of server timezone
     * Uses gmdate() which always returns UTC time
     */
    public static function getCurrentUTC() {
        return gmdate('Y-m-d H:i:s');
    }
    
    /**
     * Get current UTC timestamp
     * Unix timestamp is always UTC regardless of server timezone
     */
    public static function getCurrentUTCTimestamp() {
        return time();
    }
    
    /**
     * Get current UTC date only
     */
    public static function getCurrentUTCDate() {
        return gmdate('Y-m-d');
    }
    
    /**
     * Convert any datetime to UTC for database storage
     * 
     * @param string $datetime Input datetime
     * @param string $from_timezone Source timezone (default: Asia/Singapore)
     * @return string UTC datetime for database storage
     */
    public static function convertToUTC($datetime, $from_timezone = self::DEFAULT_TIMEZONE) {
        try {
            // Create DateTime object in source timezone
            $dt = new DateTime($datetime, new DateTimeZone($from_timezone));
            
            // Convert to UTC
            $dt->setTimezone(new DateTimeZone('UTC'));
            
            return $dt->format('Y-m-d H:i:s');
        } catch (Exception $e) {
            // Fallback: assume input is already UTC
            error_log("UTC conversion error: " . $e->getMessage());
            return $datetime;
        }
    }
    
    /**
     * Convert UTC from database to user timezone for display
     * 
     * @param string $utc_datetime UTC datetime from database
     * @param string $to_timezone Target timezone (default: user's timezone)
     * @return string Converted datetime in user's timezone
     */
    public static function convertFromUTC($utc_datetime, $to_timezone = null) {
        if ($to_timezone === null) {
            $to_timezone = self::getUserTimezone();
        }
        
        try {
            // Create DateTime object in UTC
            $dt = new DateTime($utc_datetime, new DateTimeZone('UTC'));
            
            // Convert to target timezone
            $dt->setTimezone(new DateTimeZone($to_timezone));
            
            return $dt->format('Y-m-d H:i:s');
        } catch (Exception $e) {
            error_log("UTC display conversion error: " . $e->getMessage());
            return $utc_datetime;
        }
    }
    
    /**
     * Format UTC time for user display with timezone info
     * 
     * @param string $utc_datetime UTC datetime from database
     * @param string $user_timezone User's timezone
     * @param string $format Display format
     * @return string Formatted datetime for display
     */
    public static function formatForDisplay($utc_datetime, $user_timezone = null, $format = 'M j, Y g:i A T') {
        if ($user_timezone === null) {
            $user_timezone = self::getUserTimezone();
        }
        
        try {
            $dt = new DateTime($utc_datetime, new DateTimeZone('UTC'));
            $dt->setTimezone(new DateTimeZone($user_timezone));
            
            return $dt->format($format);
        } catch (Exception $e) {
            error_log("UTC format error: " . $e->getMessage());
            return $utc_datetime;
        }
    }
    
    /**
     * Force database connection to use UTC (session level)
     * This doesn't require SUPER privileges, only affects current session
     * 
     * @param mysqli $conn Database connection
     * @return bool Success status
     */
    public static function setDatabaseUTC($conn) {
        try {
            // Set session timezone to UTC (doesn't require SUPER privilege)
            $result = $conn->query("SET time_zone = '+00:00'");
            
            if ($result) {
                return true;
            } else {
                error_log("Failed to set database session timezone: " . $conn->error);
                return false;
            }
        } catch (Exception $e) {
            error_log("Database timezone error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get user timezone from various sources
     * Priority: 1) User profile, 2) Session, 3) Default
     * 
     * @param int $user_id Optional user ID to get timezone from profile
     * @return string User's timezone
     */
    public static function getUserTimezone($user_id = null) {
        // Start session if not already started
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        // Check user profile if user_id provided
        if ($user_id) {
            $profile_timezone = self::getUserTimezoneFromProfile($user_id);
            if ($profile_timezone) {
                // Cache in session
                $_SESSION['user_timezone'] = $profile_timezone;
                return $profile_timezone;
            }
        }
        
        // Check current user profile if logged in
        if (isset($_SESSION['user_id'])) {
            $profile_timezone = self::getUserTimezoneFromProfile($_SESSION['user_id']);
            if ($profile_timezone) {
                $_SESSION['user_timezone'] = $profile_timezone;
                return $profile_timezone;
            }
        }
        
        // Check session
        if (isset($_SESSION['user_timezone'])) {
            return $_SESSION['user_timezone'];
        }
        
        // Return default
        return self::DEFAULT_TIMEZONE;
    }
    
    /**
     * Get comprehensive worldwide country code to timezone mapping
     * Supports ALL countries that Stripe accepts
     *
     * @return array Country code to timezone mapping
     */
    public static function getCountryCodeTimezoneMapping() {
        return [
            // Asia-Pacific
            'SG' => 'Asia/Singapore',
            'TH' => 'Asia/Bangkok',
            'MY' => 'Asia/Kuala_Lumpur',
            'ID' => 'Asia/Jakarta',
            'PH' => 'Asia/Manila',
            'VN' => 'Asia/Ho_Chi_Minh',
            'KH' => 'Asia/Phnom_Penh',
            'LA' => 'Asia/Vientiane',
            'MM' => 'Asia/Yangon',
            'BN' => 'Asia/Brunei',
            'JP' => 'Asia/Tokyo',
            'KR' => 'Asia/Seoul',
            'CN' => 'Asia/Shanghai',
            'HK' => 'Asia/Hong_Kong',
            'MO' => 'Asia/Macau',
            'TW' => 'Asia/Taipei',
            'IN' => 'Asia/Kolkata',
            'PK' => 'Asia/Karachi',
            'BD' => 'Asia/Dhaka',
            'LK' => 'Asia/Colombo',
            'NP' => 'Asia/Kathmandu',
            'BT' => 'Asia/Thimphu',
            'MV' => 'Indian/Maldives',
            'AF' => 'Asia/Kabul',
            'AM' => 'Asia/Yerevan',
            'AZ' => 'Asia/Baku',
            'GE' => 'Asia/Tbilisi',
            'KZ' => 'Asia/Almaty',
            'KG' => 'Asia/Bishkek',
            'TJ' => 'Asia/Dushanbe',
            'TM' => 'Asia/Ashgabat',
            'UZ' => 'Asia/Tashkent',
            'MN' => 'Asia/Ulaanbaatar',

            // Europe
            'GB' => 'Europe/London',
            'IE' => 'Europe/Dublin',
            'FR' => 'Europe/Paris',
            'DE' => 'Europe/Berlin',
            'IT' => 'Europe/Rome',
            'ES' => 'Europe/Madrid',
            'PT' => 'Europe/Lisbon',
            'NL' => 'Europe/Amsterdam',
            'BE' => 'Europe/Brussels',
            'CH' => 'Europe/Zurich',
            'AT' => 'Europe/Vienna',
            'SE' => 'Europe/Stockholm',
            'NO' => 'Europe/Oslo',
            'DK' => 'Europe/Copenhagen',
            'FI' => 'Europe/Helsinki',
            'PL' => 'Europe/Warsaw',
            'CZ' => 'Europe/Prague',
            'SK' => 'Europe/Bratislava',
            'HU' => 'Europe/Budapest',
            'RO' => 'Europe/Bucharest',
            'BG' => 'Europe/Sofia',
            'GR' => 'Europe/Athens',
            'TR' => 'Europe/Istanbul',
            'RU' => 'Europe/Moscow',
            'UA' => 'Europe/Kiev',
            'BY' => 'Europe/Minsk',
            'LT' => 'Europe/Vilnius',
            'LV' => 'Europe/Riga',
            'EE' => 'Europe/Tallinn',
            'SI' => 'Europe/Ljubljana',
            'HR' => 'Europe/Zagreb',
            'BA' => 'Europe/Sarajevo',
            'RS' => 'Europe/Belgrade',
            'ME' => 'Europe/Podgorica',
            'MK' => 'Europe/Skopje',
            'AL' => 'Europe/Tirane',
            'MD' => 'Europe/Chisinau',
            'IS' => 'Atlantic/Reykjavik',
            'MT' => 'Europe/Malta',
            'CY' => 'Asia/Nicosia',
            'LU' => 'Europe/Luxembourg',
            'LI' => 'Europe/Vaduz',
            'AD' => 'Europe/Andorra',
            'SM' => 'Europe/San_Marino',
            'VA' => 'Europe/Vatican',
            'MC' => 'Europe/Monaco',

            // Americas - North America
            'US' => 'America/New_York',
            'CA' => 'America/Toronto',
            'MX' => 'America/Mexico_City',
            'GT' => 'America/Guatemala',
            'BZ' => 'America/Belize',
            'SV' => 'America/El_Salvador',
            'HN' => 'America/Tegucigalpa',
            'NI' => 'America/Managua',
            'CR' => 'America/Costa_Rica',
            'PA' => 'America/Panama',
            'CU' => 'America/Havana',
            'JM' => 'America/Jamaica',
            'HT' => 'America/Port-au-Prince',
            'DO' => 'America/Santo_Domingo',
            'PR' => 'America/Puerto_Rico',
            'TT' => 'America/Port_of_Spain',
            'BB' => 'America/Barbados',
            'GD' => 'America/Grenada',
            'LC' => 'America/St_Lucia',
            'VC' => 'America/St_Vincent',
            'AG' => 'America/Antigua',
            'DM' => 'America/Dominica',
            'KN' => 'America/St_Kitts',
            'BS' => 'America/Nassau',

            // Americas - South America
            'BR' => 'America/Sao_Paulo',
            'AR' => 'America/Argentina/Buenos_Aires',
            'CL' => 'America/Santiago',
            'CO' => 'America/Bogota',
            'PE' => 'America/Lima',
            'VE' => 'America/Caracas',
            'EC' => 'America/Guayaquil',
            'BO' => 'America/La_Paz',
            'PY' => 'America/Asuncion',
            'UY' => 'America/Montevideo',
            'GY' => 'America/Guyana',
            'SR' => 'America/Paramaribo',
            'GF' => 'America/Cayenne',

            // Oceania
            'AU' => 'Australia/Sydney',
            'NZ' => 'Pacific/Auckland',
            'FJ' => 'Pacific/Fiji',
            'PG' => 'Pacific/Port_Moresby',
            'SB' => 'Pacific/Guadalcanal',
            'VU' => 'Pacific/Efate',
            'NC' => 'Pacific/Noumea',
            'PF' => 'Pacific/Tahiti',
            'CK' => 'Pacific/Rarotonga',
            'TO' => 'Pacific/Tongatapu',
            'WS' => 'Pacific/Apia',
            'KI' => 'Pacific/Tarawa',
            'TV' => 'Pacific/Funafuti',
            'NR' => 'Pacific/Nauru',
            'PW' => 'Pacific/Palau',
            'FM' => 'Pacific/Chuuk',
            'MH' => 'Pacific/Majuro',

            // Africa
            'ZA' => 'Africa/Johannesburg',
            'EG' => 'Africa/Cairo',
            'NG' => 'Africa/Lagos',
            'KE' => 'Africa/Nairobi',
            'MA' => 'Africa/Casablanca',
            'DZ' => 'Africa/Algiers',
            'TN' => 'Africa/Tunis',
            'LY' => 'Africa/Tripoli',
            'ET' => 'Africa/Addis_Ababa',
            'GH' => 'Africa/Accra',
            'SN' => 'Africa/Dakar',
            'ML' => 'Africa/Bamako',
            'BF' => 'Africa/Ouagadougou',
            'NE' => 'Africa/Niamey',
            'TD' => 'Africa/Ndjamena',
            'SD' => 'Africa/Khartoum',
            'SS' => 'Africa/Juba',
            'ER' => 'Africa/Asmara',
            'DJ' => 'Africa/Djibouti',
            'SO' => 'Africa/Mogadishu',
            'UG' => 'Africa/Kampala',
            'TZ' => 'Africa/Dar_es_Salaam',
            'RW' => 'Africa/Kigali',
            'BI' => 'Africa/Bujumbura',
            'CD' => 'Africa/Kinshasa',
            'CG' => 'Africa/Brazzaville',
            'CM' => 'Africa/Douala',
            'CF' => 'Africa/Bangui',
            'GA' => 'Africa/Libreville',
            'GQ' => 'Africa/Malabo',
            'ST' => 'Africa/Sao_Tome',
            'AO' => 'Africa/Luanda',
            'ZM' => 'Africa/Lusaka',
            'ZW' => 'Africa/Harare',
            'BW' => 'Africa/Gaborone',
            'NA' => 'Africa/Windhoek',
            'LS' => 'Africa/Maseru',
            'SZ' => 'Africa/Mbabane',
            'MZ' => 'Africa/Maputo',
            'MW' => 'Africa/Blantyre',
            'MG' => 'Indian/Antananarivo',
            'MU' => 'Indian/Mauritius',
            'SC' => 'Indian/Mahe',
            'KM' => 'Indian/Comoro',
            'CV' => 'Atlantic/Cape_Verde',
            'GW' => 'Africa/Bissau',
            'GM' => 'Africa/Banjul',
            'SL' => 'Africa/Freetown',
            'LR' => 'Africa/Monrovia',
            'CI' => 'Africa/Abidjan',
            'TG' => 'Africa/Lome',
            'BJ' => 'Africa/Porto-Novo',

            // Middle East
            'SA' => 'Asia/Riyadh',
            'AE' => 'Asia/Dubai',
            'QA' => 'Asia/Qatar',
            'KW' => 'Asia/Kuwait',
            'BH' => 'Asia/Bahrain',
            'OM' => 'Asia/Muscat',
            'YE' => 'Asia/Aden',
            'JO' => 'Asia/Amman',
            'LB' => 'Asia/Beirut',
            'SY' => 'Asia/Damascus',
            'IQ' => 'Asia/Baghdad',
            'IR' => 'Asia/Tehran',
            'IL' => 'Asia/Jerusalem',
            'PS' => 'Asia/Gaza',
        ];
    }

    /**
     * Get country name to timezone mapping (for backward compatibility)
     *
     * @return array Country name to timezone mapping
     */
    public static function getCountryNameTimezoneMapping() {
        return [
            // Asia
            'Singapore' => 'Asia/Singapore',
            'Thailand' => 'Asia/Bangkok',
            'Malaysia' => 'Asia/Kuala_Lumpur',
            'Indonesia' => 'Asia/Jakarta',
            'Philippines' => 'Asia/Manila',
            'Vietnam' => 'Asia/Ho_Chi_Minh',
            'Cambodia' => 'Asia/Phnom_Penh',
            'Laos' => 'Asia/Vientiane',
            'Myanmar' => 'Asia/Yangon',
            'Brunei' => 'Asia/Brunei',
            'Japan' => 'Asia/Tokyo',
            'South Korea' => 'Asia/Seoul',
            'China' => 'Asia/Shanghai',
            'Hong Kong' => 'Asia/Hong_Kong',
            'Taiwan' => 'Asia/Taipei',
            'India' => 'Asia/Kolkata',
            'Pakistan' => 'Asia/Karachi',
            'Bangladesh' => 'Asia/Dhaka',
            'Sri Lanka' => 'Asia/Colombo',
            'Nepal' => 'Asia/Kathmandu',
            'Bhutan' => 'Asia/Thimphu',
            'Maldives' => 'Indian/Maldives',

            // Europe
            'United Kingdom' => 'Europe/London',
            'Ireland' => 'Europe/Dublin',
            'France' => 'Europe/Paris',
            'Germany' => 'Europe/Berlin',
            'Italy' => 'Europe/Rome',
            'Spain' => 'Europe/Madrid',
            'Portugal' => 'Europe/Lisbon',
            'Netherlands' => 'Europe/Amsterdam',
            'Belgium' => 'Europe/Brussels',
            'Switzerland' => 'Europe/Zurich',
            'Austria' => 'Europe/Vienna',
            'Sweden' => 'Europe/Stockholm',
            'Norway' => 'Europe/Oslo',
            'Denmark' => 'Europe/Copenhagen',
            'Finland' => 'Europe/Helsinki',
            'Poland' => 'Europe/Warsaw',
            'Czech Republic' => 'Europe/Prague',
            'Hungary' => 'Europe/Budapest',
            'Romania' => 'Europe/Bucharest',
            'Bulgaria' => 'Europe/Sofia',
            'Greece' => 'Europe/Athens',
            'Turkey' => 'Europe/Istanbul',
            'Russia' => 'Europe/Moscow',

            // Americas
            'United States' => 'America/New_York',
            'Canada' => 'America/Toronto',
            'Mexico' => 'America/Mexico_City',
            'Brazil' => 'America/Sao_Paulo',
            'Argentina' => 'America/Argentina/Buenos_Aires',
            'Chile' => 'America/Santiago',
            'Colombia' => 'America/Bogota',
            'Peru' => 'America/Lima',
            'Venezuela' => 'America/Caracas',
            'Ecuador' => 'America/Guayaquil',
            'Bolivia' => 'America/La_Paz',
            'Paraguay' => 'America/Asuncion',
            'Uruguay' => 'America/Montevideo',

            // Oceania
            'Australia' => 'Australia/Sydney',
            'New Zealand' => 'Pacific/Auckland',
            'Fiji' => 'Pacific/Fiji',
            'Papua New Guinea' => 'Pacific/Port_Moresby',

            // Africa
            'South Africa' => 'Africa/Johannesburg',
            'Egypt' => 'Africa/Cairo',
            'Nigeria' => 'Africa/Lagos',
            'Kenya' => 'Africa/Nairobi',
            'Morocco' => 'Africa/Casablanca',
            'Algeria' => 'Africa/Algiers',
            'Tunisia' => 'Africa/Tunis',
            'Libya' => 'Africa/Tripoli',
            'Ethiopia' => 'Africa/Addis_Ababa',
            'Ghana' => 'Africa/Accra',

            // Middle East
            'Saudi Arabia' => 'Asia/Riyadh',
            'United Arab Emirates' => 'Asia/Dubai',
            'Qatar' => 'Asia/Qatar',
            'Kuwait' => 'Asia/Kuwait',
            'Bahrain' => 'Asia/Bahrain',
            'Oman' => 'Asia/Muscat',
            'Yemen' => 'Asia/Aden',
            'Jordan' => 'Asia/Amman',
            'Lebanon' => 'Asia/Beirut',
            'Syria' => 'Asia/Damascus',
            'Iraq' => 'Asia/Baghdad',
            'Iran' => 'Asia/Tehran',
            'Israel' => 'Asia/Jerusalem',
        ];
    }

    /**
     * Get timezone from country (supports both country codes like 'SG' and names like 'Singapore')
     *
     * @param string $country Country code (SG, TH, US) or country name (Singapore, Thailand)
     * @return string|null Timezone or null if not found
     */
    public static function getTimezoneFromCountry($country) {
        if (empty($country)) return null;

        // First try country codes (SG, TH, US, etc.)
        $code_mapping = self::getCountryCodeTimezoneMapping();
        if (isset($code_mapping[$country])) {
            return $code_mapping[$country];
        }

        // Then try country names (Singapore, Thailand, etc.)
        $name_mapping = self::getCountryNameTimezoneMapping();
        if (isset($name_mapping[$country])) {
            return $name_mapping[$country];
        }

        return null;
    }

    /**
     * Auto-generate timezone from country code during user creation
     *
     * @param string $country_code Country code from Stripe (SG, TH, US, etc.)
     * @param int $user_id User ID to update
     * @return string|null Generated timezone or null if not found
     */
    public static function autoGenerateTimezoneFromCountry($country_code, $user_id = null) {
        $timezone = self::getTimezoneFromCountry($country_code);

        if ($timezone && $user_id) {
            // Save the detected timezone to user profile
            self::saveUserTimezone($user_id, $timezone);
        }

        return $timezone;
    }

    /**
     * Get user timezone from database profile (including country-based detection)
     *
     * @param int $user_id User ID
     * @return string|null User's timezone or null if not found
     */
    public static function getUserTimezoneFromProfile($user_id) {
        try {
            global $conn;
            if (!$conn) return null;

            // Check if timezone column exists first
            $check_column = $conn->query("SHOW COLUMNS FROM user LIKE 'timezone'");
            $has_timezone_column = ($check_column && $check_column->num_rows > 0);

            // Prepare SQL based on whether timezone column exists
            if ($has_timezone_column) {
                $sql = "SELECT timezone, country FROM user WHERE id = ?";
            } else {
                $sql = "SELECT country FROM user WHERE id = ?";
            }

            $stmt = $conn->prepare($sql);
            $stmt->bind_param("i", $user_id);
            $stmt->execute();
            $result = $stmt->get_result();
            $user = $result->fetch_assoc();

            if (!$user) return null;

            // Priority 1: Check if user has explicit timezone set (if column exists)
            if ($has_timezone_column && !empty($user['timezone'])) {
                return $user['timezone'];
            }

            // Priority 2: Get timezone from country
            if (!empty($user['country'])) {
                $timezone_from_country = self::getTimezoneFromCountry($user['country']);
                if ($timezone_from_country) {
                    // Auto-save the detected timezone for future use (if timezone column exists)
                    if ($has_timezone_column) {
                        self::saveUserTimezone($user_id, $timezone_from_country);
                    }
                    return $timezone_from_country;
                }
            }

            return null;
        } catch (Exception $e) {
            error_log("Error getting user timezone: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Save user timezone to profile and session
     * 
     * @param int $user_id User ID
     * @param string $timezone Timezone to save
     * @return bool Success status
     */
    public static function saveUserTimezone($user_id, $timezone) {
        try {
            global $conn;
            if (!$conn) return false;
            
            // Validate timezone
            if (!in_array($timezone, timezone_identifiers_list())) {
                return false;
            }
            
            // Check if timezone column exists, create if not
            $check_column = $conn->query("SHOW COLUMNS FROM user LIKE 'timezone'");
            if (!$check_column || $check_column->num_rows == 0) {
                $conn->query("ALTER TABLE user ADD COLUMN timezone VARCHAR(50) DEFAULT '" . self::DEFAULT_TIMEZONE . "'");
            }
            
            // Update user timezone
            $sql = "UPDATE user SET timezone = ? WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("si", $timezone, $user_id);
            $success = $stmt->execute();
            
            if ($success) {
                // Also save to session
                if (session_status() === PHP_SESSION_NONE) {
                    session_start();
                }
                $_SESSION['user_timezone'] = $timezone;
            }
            
            return $success;
        } catch (Exception $e) {
            error_log("Error saving user timezone: " . $e->getMessage());
            return false;
        }
    }
}

} // End of class_exists('UTCTimeHelper') check

// Helper functions for backward compatibility and ease of use
if (!function_exists('getCurrentUTC')) {
    function getCurrentUTC() {
        return UTCTimeHelper::getCurrentUTC();
    }
}

if (!function_exists('convertToUTC')) {
    function convertToUTC($datetime, $from_timezone = 'Asia/Singapore') {
        return UTCTimeHelper::convertToUTC($datetime, $from_timezone);
    }
}

if (!function_exists('convertFromUTC')) {
    function convertFromUTC($utc_datetime, $to_timezone = null) {
        return UTCTimeHelper::convertFromUTC($utc_datetime, $to_timezone);
    }
}

if (!function_exists('formatTimeForDisplay')) {
    function formatTimeForDisplay($utc_datetime, $user_timezone = null, $format = 'M j, Y g:i A T') {
        return UTCTimeHelper::formatForDisplay($utc_datetime, $user_timezone, $format);
    }
}

if (!function_exists('getUserTimezone')) {
    function getUserTimezone($user_id = null) {
        return UTCTimeHelper::getUserTimezone($user_id);
    }
}

if (!function_exists('setDatabaseUTC')) {
    function setDatabaseUTC($conn) {
        return UTCTimeHelper::setDatabaseUTC($conn);
    }
}

// Helper function for auto-generating timezone during user creation
if (!function_exists('autoGenerateUserTimezone')) {
    function autoGenerateUserTimezone($country_code, $user_id = null) {
        return UTCTimeHelper::autoGenerateTimezoneFromCountry($country_code, $user_id);
    }
}

/**
 * CUSTOMER-SPECIFIC TIMEZONE FUNCTIONS
 * These functions are specifically designed for customer front-end pages
 */

/**
 * Get customer's timezone (for front-end customer pages only)
 * Priority: 1) Customer profile, 2) Session, 3) Browser detection, 4) Default
 *
 * @return string Customer's timezone
 */
if (!function_exists('getCustomerTimezone')) {
    function getCustomerTimezone() {
        // Start session if not already started
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // Check if customer is logged in and get from profile
        if (isset($_SESSION['user_id'])) {
            $customer_timezone = UTCTimeHelper::getUserTimezoneFromProfile($_SESSION['user_id']);
            if ($customer_timezone) {
                $_SESSION['customer_timezone'] = $customer_timezone;
                return $customer_timezone;
            }
        }

        // Check session
        if (isset($_SESSION['customer_timezone'])) {
            return $_SESSION['customer_timezone'];
        }

        // Check if timezone was detected by JavaScript
        if (isset($_SESSION['user_timezone'])) {
            return $_SESSION['user_timezone'];
        }

        // Return default for customers (Singapore)
        return 'Asia/Singapore';
    }
}

/**
 * Display time in customer's timezone (for customer pages)
 *
 * @param string $utc_datetime UTC datetime from database
 * @param string $format Display format (default: user-friendly)
 * @return string Formatted time in customer's timezone
 */
if (!function_exists('showCustomerTime')) {
    function showCustomerTime($utc_datetime, $format = 'M j, Y g:i A') {
        $customer_timezone = getCustomerTimezone();
        return UTCTimeHelper::formatForDisplay($utc_datetime, $customer_timezone, $format);
    }
}

/**
 * Display time in customer's timezone (simple format)
 *
 * @param string $utc_datetime UTC datetime from database
 * @return string Simple formatted time (Y-m-d H:i)
 */
if (!function_exists('showCustomerTimeSimple')) {
    function showCustomerTimeSimple($utc_datetime) {
        $customer_timezone = getCustomerTimezone();
        return UTCTimeHelper::formatForDisplay($utc_datetime, $customer_timezone, 'Y-m-d H:i');
    }
}

/**
 * Display relative time for customers (e.g., "2 hours ago")
 *
 * @param string $utc_datetime UTC datetime from database
 * @return string Relative time string
 */
if (!function_exists('showCustomerTimeRelative')) {
    function showCustomerTimeRelative($utc_datetime) {
        try {
            $customer_timezone = getCustomerTimezone();

            // Convert UTC to customer timezone
            $dt = new DateTime($utc_datetime, new DateTimeZone('UTC'));
            $dt->setTimezone(new DateTimeZone($customer_timezone));

            // Get current time in customer timezone
            $now = new DateTime('now', new DateTimeZone($customer_timezone));

            $diff = $now->diff($dt);

            if ($diff->days > 0) {
                return $diff->days . ' day' . ($diff->days > 1 ? 's' : '') . ' ago';
            } elseif ($diff->h > 0) {
                return $diff->h . ' hour' . ($diff->h > 1 ? 's' : '') . ' ago';
            } elseif ($diff->i > 0) {
                return $diff->i . ' minute' . ($diff->i > 1 ? 's' : '') . ' ago';
            } else {
                return 'Just now';
            }
        } catch (Exception $e) {
            return showCustomerTime($utc_datetime);
        }
    }
}

/**
 * Get customer's current local time
 *
 * @param string $format Display format
 * @return string Current time in customer's timezone
 */
if (!function_exists('getCustomerCurrentTime')) {
    function getCustomerCurrentTime($format = 'Y-m-d H:i:s') {
        $customer_timezone = getCustomerTimezone();
        $dt = new DateTime('now', new DateTimeZone($customer_timezone));
        return $dt->format($format);
    }
}

/**
 * Save customer's timezone preference
 *
 * @param string $timezone Timezone to save
 * @return bool Success status
 */
if (!function_exists('saveCustomerTimezone')) {
    function saveCustomerTimezone($timezone) {
        // Start session if not already started
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // Validate timezone
        if (!in_array($timezone, timezone_identifiers_list())) {
            return false;
        }

        // Save to session
        $_SESSION['customer_timezone'] = $timezone;
        $_SESSION['user_timezone'] = $timezone; // For compatibility

        // If customer is logged in, save to database
        if (isset($_SESSION['user_id'])) {
            return UTCTimeHelper::saveUserTimezone($_SESSION['user_id'], $timezone);
        }

        return true;
    }
}

// Initialize timezone configuration when helper is loaded
if (!function_exists('initializeTimezoneConfig')) {
    function initializeTimezoneConfig() {
        global $conn;

        // Set PHP timezone to UTC for consistent behavior
        date_default_timezone_set('UTC');

        // Try to set database session timezone to UTC if connection exists
        if (isset($conn) && $conn) {
            UTCTimeHelper::setDatabaseUTC($conn);
        }
    }
}

// Auto-initialize when helper is loaded
initializeTimezoneConfig();

// Log that helper is loaded
error_log("UTC Time Helper loaded - Server timezone independent operations ready");
?>
