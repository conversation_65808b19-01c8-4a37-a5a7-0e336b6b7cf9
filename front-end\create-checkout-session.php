<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Auto-detect environment for file paths
$is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
$file_base_path = $is_localhost ? '/helloit' : '';

try {
    require_once $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/vendor/autoload.php'; // Stripe PHP SDK
    include($_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/functions/server.php');
    session_start();
} catch (Exception $e) {
    die("Error loading files: " . $e->getMessage());
}

\Stripe\Stripe::setApiKey('sk_test_51ROUofEJRyUMDOj5Op3iqq0gpje1mQqwhINGCYVwVRvmOixzZh1IUmfwArcDt7FtJkN9aszopPP6dyxVWyOZeC8F00gwZtB0ju'); // <-- Replace with your Stripe Secret Key

$user_id = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;

// Get save_payment_method preference from POST data
// Use the hidden field value which is always sent (1 or 0)
$save_payment_method = isset($_POST['save_payment_method']) ? ($_POST['save_payment_method'] === '1') : false; // Default to NOT save
$cart_items = [];

if ($user_id) {
    // Fetch cart items from database for logged-in user
    $query = "SELECT c.cart_id, ci.id AS cart_item_id, ci.ticket_id, ci.quantity, t.ticket_type, t.dollar_price_per_package, t.package_size, t.numbers_per_package
              FROM cart c
              JOIN cart_items ci ON c.cart_id = ci.cart_id
              JOIN tickets t ON ci.ticket_id = t.ticketid
              WHERE c.user_id = ? AND c.status = 'active'";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $cart_items = $result->fetch_all(MYSQLI_ASSOC);
} else {
    // Guest: get cart from session - DO NOT GROUP, keep items separate
    $cart_items = isset($_SESSION['guest_cart']) ? $_SESSION['guest_cart'] : [];

    // Ensure every guest cart item has package_size and numbers_per_package
    foreach ($cart_items as &$item) {
        if ((empty($item['package_size']) || empty($item['numbers_per_package'])) && !empty($item['ticket_id'])) {
            $stmt = $conn->prepare("SELECT package_size, numbers_per_package FROM tickets WHERE ticketid = ?");
            $stmt->bind_param("i", $item['ticket_id']);
            $stmt->execute();
            $stmt->bind_result($package_size, $numbers_per_package);
            if ($stmt->fetch()) {
                if (empty($item['package_size'])) {
                    $item['package_size'] = $package_size;
                }
                if (empty($item['numbers_per_package'])) {
                    $item['numbers_per_package'] = $numbers_per_package;
                }
            }
            $stmt->close();
        }
    }
    unset($item); // break reference
}

$line_items = [];
$cart_metadata = []; // Store cart data for webhook processing

foreach ($cart_items as $item) {
    // Get numbers_per_package from tickets table if not present
    if (!isset($item['numbers_per_package'])) {
        $stmt = $conn->prepare("SELECT numbers_per_package FROM tickets WHERE ticketid = ?");
        $stmt->bind_param("i", $item['ticket_id']);
        $stmt->execute();
        $stmt->bind_result($numbers_per_package);
        if ($stmt->fetch()) {
            $item['numbers_per_package'] = $numbers_per_package;
        }
        $stmt->close();
    }

    // Create descriptive product name that includes package size
    $product_name = $item['ticket_type'];
    if (!empty($item['package_size'])) {
        $product_name .= ' ' . $item['package_size'];
    }

    $product_data = [
        'name' => $product_name
    ];
    if (!empty($item['package_size'])) {
        $tickets_text = ($item['numbers_per_package'] ?? 1) == 1 ? 'ticket' : 'tickets';
        $product_data['description'] = ($item['numbers_per_package'] ?? 1) . ' ' . $tickets_text;
    }
    $line_items[] = [
        'price_data' => [
            'currency' => 'usd',
            'product_data' => $product_data,
            'unit_amount' => $item['dollar_price_per_package'] * 100,
        ],
        'quantity' => $item['quantity'],
    ];

    // Store cart item data for webhook processing
    $cart_metadata[] = [
        'ticket_type' => $item['ticket_type'],
        'package_size' => $item['package_size'] ?? '',
        'numbers_per_package' => $item['numbers_per_package'] ?? 1,
        'dollar_price_per_package' => $item['dollar_price_per_package'],
        'quantity' => $item['quantity']
    ];
}

// Determine base URL dynamically
if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
    $protocol = "https://";
} else {
    $protocol = "http://";
}
$host = $_SERVER['HTTP_HOST'];
$basePath = $file_base_path . '/front-end/'; // Environment-aware path

// Check if this is coming from pre-checkout (Add New Card flow)
$is_pre_checkout = isset($_POST['source']) && $_POST['source'] === 'pre_checkout';
$is_pre_checkout = $is_pre_checkout || (isset($_SERVER['HTTP_REFERER']) && strpos($_SERVER['HTTP_REFERER'], 'pre-checkout.php') !== false);

// Determine success URL based on user login status and flow type
if ($is_pre_checkout && $user_id) {
    // For logged-in users coming from pre-checkout, use cart-payment-success.php
    $success_url = $protocol . $host . $file_base_path . '/functions/cart-payment-success.php?session_id={CHECKOUT_SESSION_ID}';
} else {
    // For guest users (regardless of pre-checkout) and regular cart checkout, use payment-success.php
    $success_url = $protocol . $host . $basePath . 'payment-success.php?session_id={CHECKOUT_SESSION_ID}';
}
$cancel_url = $protocol . $host . $basePath . 'cancel.php';

$customer_id = null;
if ($user_id) {
    // For logged-in users, get or create Stripe customer
    $stmt = $conn->prepare("SELECT email, stripe_customer_id FROM user WHERE id = ?");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $stmt->bind_result($user_email, $stripe_customer_id);
    $stmt->fetch();
    $stmt->close();

    // If no Stripe customer ID, create one and save it
    if (!$stripe_customer_id) {
        $customer = \Stripe\Customer::create([
            'email' => $user_email,
            'metadata' => [
                'user_id' => $user_id
            ]
        ]);
        $stripe_customer_id = $customer->id;
        $update = $conn->prepare("UPDATE user SET stripe_customer_id = ? WHERE id = ?");
        $update->bind_param("si", $stripe_customer_id, $user_id);
        $update->execute();
        $update->close();
    }
    $customer_id = $stripe_customer_id;
} else {
    // For guest users, don't create customer here - let webhook handle it
    // This ensures proper customer creation with billing address
    $customer_id = null;
}

// Store cart data in database to avoid Stripe 500 character limit
$cart_session_id = 'cart_' . uniqid() . '_' . time();
$cart_data_json = json_encode($cart_metadata);

// Insert cart data into database
$insert_cart_query = "INSERT INTO cart_sessions (session_id, cart_data, user_id) VALUES (?, ?, ?)";
$cart_stmt = $conn->prepare($insert_cart_query);
$cart_stmt->bind_param("ssi", $cart_session_id, $cart_data_json, $user_id);
$cart_stmt->execute();

// Get cart_id for pre-checkout flows (required by cart-payment-success.php)
$cart_id = '';
if ($is_pre_checkout && $user_id) {
    $cart_query = "SELECT cart_id FROM cart WHERE user_id = ? AND status = 'active' LIMIT 1";
    $cart_stmt = $conn->prepare($cart_query);
    $cart_stmt->bind_param("i", $user_id);
    $cart_stmt->execute();
    $cart_result = $cart_stmt->get_result();
    if ($cart_row = $cart_result->fetch_assoc()) {
        $cart_id = $cart_row['cart_id'];
    }
}

// Create minimal metadata (well under 500 character limit)
$metadata = [
    'user_id' => $user_id ?? 'guest',
    'username' => isset($_SESSION['username']) ? $_SESSION['username'] : '',
    'total_items' => count($cart_metadata),
    'cart_session_id' => $cart_session_id, // Reference to database
    'total_amount' => array_sum(array_column($cart_metadata, 'dollar_price_per_package')),
    'save_payment_method' => $save_payment_method ? '1' : '0'
];

// Add cart_id for pre-checkout flows (required by cart-payment-success.php)
if ($is_pre_checkout && !empty($cart_id)) {
    $metadata['cart_id'] = $cart_id;
}

// Get guest email for processing
$guest_email = isset($_POST['guest_email']) ? trim($_POST['guest_email']) : '';

// Add guest email to metadata if provided
if (!empty($guest_email) && filter_var($guest_email, FILTER_VALIDATE_EMAIL)) {
    $metadata['guest_email'] = $guest_email;
}

// Debug: Log the metadata being sent to Stripe
error_log("Creating Stripe session with metadata: " . json_encode($metadata));
error_log("Cart session ID: " . $cart_session_id);
error_log("SUCCESS URL LOGIC: is_pre_checkout=" . ($is_pre_checkout ? 'true' : 'false') . ", user_id=" . ($user_id ?? 'null') . ", success_url=" . $success_url);

// Debug output for troubleshooting (commented out to prevent header issues)
// echo "<h3>Debug Information:</h3>";
// echo "<p>User ID: " . ($user_id ? $user_id : 'Guest') . "</p>";
// echo "<p>Cart items count: " . count($cart_items) . "</p>";
// echo "<p>Line items count: " . count($line_items) . "</p>";
// echo "<p>Success URL: " . $success_url . "</p>";
// echo "<p>Cancel URL: " . $cancel_url . "</p>";
// echo "<p>File base path: " . $file_base_path . "</p>";
// echo "<p>About to create Stripe session...</p>";
// flush();

$session_params = [
    'payment_method_types' => ['card'],
    'line_items' => $line_items,
    'mode' => 'payment',
    'success_url' => $success_url,
    'cancel_url' => $cancel_url,
    'billing_address_collection' => 'required',
    'customer_email' => null,
    'metadata' => $metadata
];

if ($customer_id) {
    $session_params['customer'] = $customer_id;
} else {
    // For guest users, pre-fill email if provided
    if (!empty($guest_email) && filter_var($guest_email, FILTER_VALIDATE_EMAIL)) {
        $session_params['customer_email'] = $guest_email;
        error_log("Using customer_email for guest checkout: " . $guest_email);
    }
}

// Only save payment method if checkbox was checked (works for both existing customers and new customers)
if ($save_payment_method) {
    $session_params['payment_intent_data'] = [
        'setup_future_usage' => 'off_session', // This will save the card for future use
    ];
}
try {
    $session = \Stripe\Checkout\Session::create($session_params);

    if ($session && $session->url) {
        header('Location: ' . $session->url);
        exit;
    } else {
        die("Error: Stripe session created but no URL returned");
    }
} catch (Exception $e) {
    // Parse Stripe error and show user-friendly message
    $error_message = $e->getMessage();
    $user_friendly_message = "Payment Error: ";

    // Check for specific error types
    if (stripos($error_message, 'insufficient_funds') !== false ||
        stripos($error_message, 'insufficient funds') !== false) {
        $user_friendly_message .= "Your card has insufficient funds. Please try a different payment method or contact your bank.";
    } elseif (stripos($error_message, 'card_declined') !== false ||
              stripos($error_message, 'declined') !== false) {
        $user_friendly_message .= "Your card was declined. Please try a different payment method or contact your bank.";
    } elseif (stripos($error_message, 'expired_card') !== false ||
              stripos($error_message, 'expired') !== false) {
        $user_friendly_message .= "Your card has expired. Please use a different payment method.";
    } elseif (stripos($error_message, 'incorrect_cvc') !== false ||
              stripos($error_message, 'cvc') !== false) {
        $user_friendly_message .= "The security code (CVC) is incorrect. Please check and try again.";
    } elseif (stripos($error_message, 'processing_error') !== false) {
        $user_friendly_message .= "There was a processing error. Please try again in a few minutes.";
    } elseif (stripos($error_message, 'rate_limit') !== false) {
        $user_friendly_message .= "Too many requests. Please wait a moment and try again.";
    } else {
        $user_friendly_message .= "There was an issue processing your payment. Please try again or contact support.";
    }

    // Log the actual error for debugging
    error_log("Stripe checkout session creation error: " . $error_message);

    // Redirect to cart with error message
    $redirect_url = $url_base . "/front-end/cart.php?error=" . urlencode($user_friendly_message);
    header("Location: " . $redirect_url);
    exit();
}