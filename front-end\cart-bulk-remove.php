<?php
session_start();
include($_SERVER['DOCUMENT_ROOT'] . '/helloit/functions/server.php');

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['status' => 'error', 'message' => 'Invalid request method']);
    exit;
}

$cart_item_ids_json = isset($_POST['cart_item_ids']) ? $_POST['cart_item_ids'] : '';
$cart_item_ids = json_decode($cart_item_ids_json, true);

if (!is_array($cart_item_ids) || empty($cart_item_ids)) {
    echo json_encode(['status' => 'error', 'message' => 'No items selected for removal']);
    exit;
}

$user_id = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;

try {
    if ($user_id) {
        // Handle logged-in user cart
        $conn->begin_transaction();
        
        $removed_count = 0;
        $placeholders = str_repeat('?,', count($cart_item_ids) - 1) . '?';
        
        // Prepare the query to remove multiple items
        $query = "DELETE FROM cart_items WHERE id IN ($placeholders) AND cart_id IN (SELECT cart_id FROM cart WHERE user_id = ? AND status = 'active')";
        $stmt = $conn->prepare($query);
        
        // Bind parameters: cart_item_ids + user_id
        $types = str_repeat('i', count($cart_item_ids)) . 'i';
        $params = array_merge($cart_item_ids, [$user_id]);
        $stmt->bind_param($types, ...$params);
        
        if ($stmt->execute()) {
            $removed_count = $stmt->affected_rows;
        }
        $stmt->close();
        
        $conn->commit();
        
        if ($removed_count > 0) {
            echo json_encode([
                'status' => 'success', 
                'message' => "$removed_count item(s) removed successfully",
                'removed_count' => $removed_count
            ]);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'No items were removed']);
        }
        
    } else {
        // Handle guest cart (session-based)
        if (isset($_SESSION['guest_cart']) && is_array($_SESSION['guest_cart'])) {
            $removed_count = 0;
            $new_guest_cart = [];
            
            foreach ($_SESSION['guest_cart'] as $item) {
                $item_id = isset($item['cart_item_id']) ? $item['cart_item_id'] : '';
                if (!in_array($item_id, $cart_item_ids)) {
                    $new_guest_cart[] = $item;
                } else {
                    $removed_count++;
                }
            }
            
            $_SESSION['guest_cart'] = $new_guest_cart;
            
            echo json_encode([
                'status' => 'success', 
                'message' => "$removed_count item(s) removed successfully",
                'removed_count' => $removed_count
            ]);
        } else {
            echo json_encode(['status' => 'error', 'message' => 'No cart found']);
        }
    }
} catch (Exception $e) {
    if ($user_id && isset($conn)) {
        $conn->rollback();
    }
    error_log("Cart bulk remove error: " . $e->getMessage());
    echo json_encode(['status' => 'error', 'message' => 'An error occurred while removing items']);
}
?>
