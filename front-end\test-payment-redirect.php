<?php
session_start();

// Auto-detect environment for file paths
$is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
$base_path = $is_localhost ? '/helloit' : '';

echo "<h2>Payment Redirect Test</h2>";

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo "<p style='color: red;'>❌ User not logged in</p>";
    echo "<p><a href='sign-in.php'>Login first</a></p>";
    exit;
}

echo "<p style='color: green;'>✅ User logged in: " . ($_SESSION['username'] ?? 'Unknown') . " (ID: " . $_SESSION['user_id'] . ")</p>";

// Test payment method parameter
$payment_method = isset($_GET['payment_method']) ? $_GET['payment_method'] : 'pm_test123';
echo "<p><strong>Testing with payment method:</strong> " . htmlspecialchars($payment_method) . "</p>";

// Test the redirect URL
$redirect_url = $base_path . '/front-end/create-pre-checkout-payment.php?payment_method=' . urlencode($payment_method);
echo "<p><strong>Redirect URL:</strong> " . htmlspecialchars($redirect_url) . "</p>";

// Check if the file exists
$file_path = $_SERVER['DOCUMENT_ROOT'] . $base_path . '/front-end/create-pre-checkout-payment.php';
if (file_exists($file_path)) {
    echo "<p style='color: green;'>✅ Payment file exists: " . $file_path . "</p>";
} else {
    echo "<p style='color: red;'>❌ Payment file NOT found: " . $file_path . "</p>";
}

// Test direct access
echo "<h3>Test Direct Access:</h3>";
echo "<p><a href='" . $redirect_url . "' target='_blank'>Click here to test direct access</a></p>";

// Test with JavaScript redirect
echo "<h3>Test JavaScript Redirect:</h3>";
echo "<button onclick='testRedirect()'>Test JavaScript Redirect</button>";
echo "<div id='result'></div>";

?>

<script>
function testRedirect() {
    const basePath = '<?php echo $base_path; ?>';
    const paymentMethod = '<?php echo $payment_method; ?>';
    const redirectUrl = basePath + '/front-end/create-pre-checkout-payment.php?payment_method=' + paymentMethod;
    
    console.log('Testing redirect to:', redirectUrl);
    document.getElementById('result').innerHTML = '<p>Redirecting to: ' + redirectUrl + '</p>';
    
    // Redirect after 2 seconds
    setTimeout(function() {
        window.location.href = redirectUrl;
    }, 2000);
}
</script>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
button { background: #007bff; color: white; padding: 10px 15px; border: none; border-radius: 4px; cursor: pointer; }
</style>
