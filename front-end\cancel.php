<?php
session_start();
include('../functions/server.php');

// Auto-detect environment for URL paths
$is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
$url_base = $is_localhost ? '/helloit' : '';

// Check for specific error reasons from URL parameters
$error_reason = $_GET['error'] ?? '';
$payment_intent = $_GET['payment_intent'] ?? '';
$canceled = $_GET['canceled'] ?? '';

// Default messages
$title = "Payment Canceled";
$message = "Your payment process was canceled. No charges have been made to your account.";
$icon_class = "fas fa-window-close";
$icon_color = "red";

// Handle general cancellation (when user clicks Back button)
if ($canceled === 'true' && empty($error_reason)) {
    $title = "Checkout Canceled";
    $message = "You chose to go back from the payment page. Your cart items are still saved and ready for checkout when you're ready.";
    $icon_class = "fas fa-window-close";
    $icon_color = "red";
}

// Handle specific error cases
if (!empty($error_reason)) {
    switch ($error_reason) {
        case 'insufficient_funds':
            $title = "Insufficient Funds";
            $message = "Your card has insufficient funds to complete this purchase. Please try a different payment method or contact your bank.";
            $icon_class = "fas fa-credit-card";
            $icon_color = "red";
            break;
        case 'card_declined':
            $title = "Card Declined";
            $message = "Your card was declined by your bank. Please try a different payment method or contact your bank for more information.";
            $icon_class = "fas fa-ban";
            $icon_color = "red";
            break;
        case 'expired_card':
            $title = "Card Expired";
            $message = "Your card has expired. Please use a different payment method with a valid expiration date.";
            $icon_class = "fas fa-calendar-times";
            $icon_color = "red";
            break;
        case 'incorrect_cvc':
            $title = "Security Code Error";
            $message = "The security code (CVC) you entered is incorrect. Please check your card and try again.";
            $icon_class = "fas fa-shield-alt";
            $icon_color = "red";
            break;
        case 'processing_error':
            $title = "Processing Error";
            $message = "There was a temporary processing error. Please try again in a few minutes.";
            $icon_class = "fas fa-exclamation-triangle";
            $icon_color = "red";
            break;
        default:
            // Keep default values
            break;
    }
}

// If payment_intent is provided, we can potentially get more specific error info
if (!empty($payment_intent)) {
    error_log("Payment canceled with payment_intent: $payment_intent, error: $error_reason");
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Canceled</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap , fonts & icons  -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Plugin'stylesheets  -->
    <link rel="stylesheet" href="../plugins/aos/aos.min.css">
    <link rel="stylesheet" href="../plugins/fancybox/jquery.fancybox.min.css">
    <link rel="stylesheet" href="../plugins/nice-select/nice-select.min.css">
    <link rel="stylesheet" href="../plugins/slick/slick.min.css">
    <link rel="stylesheet" href="../plugins/date-picker/css/gijgo.min.css" type="text/css" />
    <!-- Vendor stylesheets  -->
    <link rel="stylesheet" href="../plugins/theme-mode-switcher/switcher-panel.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/theme-mode-custom.css">
    <style>
    .cancel-container {
        max-width: 600px;
        margin: 50px auto;
        padding: 30px;
        text-align: center;
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    @media (min-width: 767px) {
        .cancel-container {
            margin-top: 120px !important;
        }
    }

    .cancel-icon {
        font-size: 60px;
        color: <?php echo $icon_color; ?>;
        margin-bottom: 20px;
    }

    .cancel-title {
        font-size: 28px;
        font-weight: 600;
        margin-bottom: 15px;
        color: #333;
    }

    .cancel-message {
        font-size: 16px;
        color: #666;
        margin-bottom: 25px;
    }

    .btn-return {
        background-color: #6754e2;
        color: white;
        padding: 10px 25px;
        border-radius: 5px;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
        display: inline-block;
        margin: 10px;
    }

    .btn-return:hover {
        background-color: #5344c9;
        transform: translateY(-2px);
        color: white;
        text-decoration: none;
    }
    </style>
</head>

<body data-theme="light">
    <div class="site-wrapper overflow-hidden">
        <!-- Header Area -->
        <?php include('../header-footer/newnavtest.php'); ?>

        <div class="cancel-container">
            <div class="cancel-icon">
                <i class="<?php echo $icon_class; ?>"></i>
            </div>
            <h1 class="cancel-title"><?php echo htmlspecialchars($title); ?></h1>
            <p class="cancel-message"><?php echo htmlspecialchars($message); ?></p>
            <div class="action-buttons">
                <a href="cart.php" class="btn-return"><i class="fas fa-shopping-cart"></i> Return to Cart</a>
                <a href="buy-now.php" class="btn-return"><i class="fas fa-tag"></i> View Plans</a>
                <?php if ($canceled === 'true' && empty($error_reason)): ?>
                <a href="pre-checkout.php" class="btn-return"><i class="fas fa-credit-card"></i> Try Checkout Again</a>
                <?php endif; ?>
            </div>

            <?php if ($canceled === 'true' && empty($error_reason)): ?>
            <div style="margin-top: 20px; padding: 15px; background-color: #e7f3ff; border-radius: 8px; border-left: 4px solid #2196F3;">
                <p style="margin: 0; color: #1976D2; font-size: 14px;">
                    <i class="fas fa-info-circle" style="margin-right: 8px;"></i>
                    <strong>Your cart is still saved!</strong>
                    <br>
                    You can continue shopping or try checkout again when you're ready.
                </p>
            </div>
            <?php endif; ?>
        </div>

        <!-- Footer section -->
        <?php include('../header-footer/footer.php'); ?>
    </div>

    <!-- Vendor Scripts -->
    <script src="../js/vendor.min.js"></script>
    <!-- Plugin's Scripts -->
    <script src="../plugins/fancybox/jquery.fancybox.min.js"></script>
    <script src="../plugins/nice-select/jquery.nice-select.min.js"></script>
    <script src="../plugins/aos/aos.min.js"></script>
    <script src="../plugins/slick/slick.min.js"></script>
    <script src="../plugins/date-picker/js/gijgo.min.js"></script>
    <script src="../plugins/counter-up/jquery.waypoints.min.js"></script>
    <script src="../plugins/counter-up/jquery.counterup.min.js"></script>
    <script src="../plugins/theme-mode-switcher/gr-theme-mode-switcher.js"></script>
    <!-- Activation Script -->
    <script src="../js/custom.js"></script>
</body>

</html>