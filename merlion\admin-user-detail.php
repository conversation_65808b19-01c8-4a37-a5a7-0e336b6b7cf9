<?php
session_start();
include('../functions/server.php');
include('../functions/timezone-helper.php'); // Include timezone helper for proper time handling
require_once '../vendor/autoload.php'; // Include Composer autoloader for Guzzle

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('location: admin-login');
    exit();
}

$admin_username = $_SESSION['admin_username'];
$admin_id = $_SESSION['admin_id'];
$admin_role = $_SESSION['admin_role'];

// Load centralized API configuration
require_once '../config/api-config.php';

// Get API configuration
$apiConfig = getCustomerApiConfig();
$apiEndpoint = $apiConfig['endpoint'];
$apiPath = $apiConfig['path'];
$apiKey = $apiConfig['key'];

// Function to send user data to Appika API
function sendToAppikaAPI($customerData, $method = 'POST', $path = '') {
    global $apiEndpoint, $apiPath, $apiKey;

    // Create a Guzzle HTTP client
    $client = new \GuzzleHttp\Client([
        'base_uri' => $apiEndpoint,
        'timeout' => 30,
        'http_errors' => false, // Don't throw exceptions for 4xx/5xx responses
    ]);

    // Determine the full path
    $fullPath = empty($path) ? $apiPath : $path;

    try {
        // Send the request
        $response = $client->request($method, $fullPath, [
            'headers' => [
                'Authorization' => "Bearer {$apiKey}",
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ],
            'json' => $customerData
        ]);

        // Get status code and response body
        $statusCode = $response->getStatusCode();
        $body = $response->getBody()->getContents();

        // Parse JSON if the response is JSON
        if (strpos($response->getHeaderLine('Content-Type'), 'application/json') !== false) {
            $data = json_decode($body, true);
            return [
                'success' => ($statusCode >= 200 && $statusCode < 300),
                'status' => $statusCode,
                'data' => $data
            ];
        } else {
            return [
                'success' => false,
                'status' => $statusCode,
                'data' => $body
            ];
        }
    } catch (\Exception $e) {
        return [
            'success' => false,
            'status' => 500,
            'error' => $e->getMessage()
        ];
    }
}

// Function to update location in Appika
function updateLocationInAppika($customerDbId, $locationId, $locationData) {
    global $apiPath;

    // Create the path for updating a location
    $locationPath = $apiPath . '/' . $customerDbId . '/locations/' . $locationId;

    // Send the location data to Appika
    return sendToAppikaAPI($locationData, 'PUT', $locationPath);
}

// Get user ID from URL
$user_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($user_id <= 0) {
    // Try to get by username
    $username = isset($_GET['username']) ? $_GET['username'] : '';
    if (!empty($username)) {
        $username_safe = mysqli_real_escape_string($conn, $username);
        $user_query = "SELECT id FROM user WHERE username = '$username_safe'";
        $user_result = mysqli_query($conn, $user_query);
        if ($user_result && mysqli_num_rows($user_result) > 0) {
            $user_id = mysqli_fetch_assoc($user_result)['id'];
        }
    }

    if ($user_id <= 0) {
        header('location: admin-users');
        exit();
    }
}

// Get user information
$user_query = "SELECT * FROM user WHERE id = $user_id";
$user_result = mysqli_query($conn, $user_query);

if (!$user_result || mysqli_num_rows($user_result) == 0) {
    header('location: admin-users');
    exit();
}

$user = mysqli_fetch_assoc($user_result);

// Get user's tickets
$tickets_query = "SELECT * FROM support_tickets WHERE user_id = $user_id ORDER BY created_at DESC LIMIT 5";
$tickets_result = mysqli_query($conn, $tickets_query);

// Get user's purchases
$purchases_query = "SELECT * FROM purchasetickets WHERE username = '{$user['username']}' ORDER BY purchase_time DESC LIMIT 5";
$purchases_result = mysqli_query($conn, $purchases_query);

// Get user's ticket logs
$logs_query = "SELECT * FROM ticket_logs WHERE user_id = $user_id ORDER BY created_at DESC LIMIT 5";
$logs_result = mysqli_query($conn, $logs_query);

// Process add tickets form and edit user form
$message = '';
$message_type = '';

// Process edit user form
if (isset($_POST['edit_user'])) {
    $edit_first_name = mysqli_real_escape_string($conn, $_POST['edit_first_name']);
    $edit_last_name = mysqli_real_escape_string($conn, $_POST['edit_last_name']);
    $edit_email = mysqli_real_escape_string($conn, $_POST['edit_email']);
    $edit_phone = mysqli_real_escape_string($conn, $_POST['edit_phone']);
    $edit_company = mysqli_real_escape_string($conn, $_POST['edit_company']);
    $edit_tax_id = mysqli_real_escape_string($conn, $_POST['edit_tax_id']);
    $edit_address = mysqli_real_escape_string($conn, $_POST['edit_address']);
    $edit_address2 = mysqli_real_escape_string($conn, $_POST['edit_address2']);
    $edit_district = mysqli_real_escape_string($conn, $_POST['edit_district']);
    $edit_city = mysqli_real_escape_string($conn, $_POST['edit_city']);
    $edit_state = mysqli_real_escape_string($conn, $_POST['edit_state']);
    $edit_postal_code = mysqli_real_escape_string($conn, $_POST['edit_postal_code']);
    $edit_country = mysqli_real_escape_string($conn, $_POST['edit_country']);
    $is_ajax = isset($_POST['ajax']) && $_POST['ajax'] == 1;

    // Validate input
    $errors = [];

    if (empty($edit_email)) {
        $errors[] = "Email is required";
    } else if (!filter_var($edit_email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = "Invalid email format";
    } else {
        // Check if email already exists for other users
        $check_email = "SELECT id FROM user WHERE email = '$edit_email' AND id != $user_id";
        $result_email = mysqli_query($conn, $check_email);
        if (mysqli_num_rows($result_email) > 0) {
            $errors[] = "Email already exists";
        }
    }

    // If no errors, update user
    if (empty($errors)) {
        $update_sql = "UPDATE user SET
                      first_name = '$edit_first_name',
                      last_name = '$edit_last_name',
                      email = '$edit_email',
                      tell = '$edit_phone',
                      company_name = '$edit_company',
                      tax_id = '$edit_tax_id',
                      address = '$edit_address',
                      address2 = '$edit_address2',
                      district = '$edit_district',
                      city = '$edit_city',
                      state = '$edit_state',
                      postal_code = '$edit_postal_code',
                      country = '$edit_country'
                      WHERE id = $user_id";

        if (mysqli_query($conn, $update_sql)) {
            // Create logs directory if it doesn't exist
            if (!file_exists("../logs")) {
                mkdir("../logs", 0755, true);
            }

            // Log file for debugging
            $log_file = fopen("../logs/appika_api_admin.log", "a");

            // Check if user has an Appika ID
            if (!empty($user['appika_id'])) {
                // First, search for the customer in Appika to get their ID
                $client = new \GuzzleHttp\Client([
                    'base_uri' => $apiEndpoint,
                    'timeout' => 30,
                    'http_errors' => false,
                ]);

                // Search for customer by appika_id
                $searchResult = $client->request('GET', $apiPath, [
                    'headers' => [
                        'Authorization' => "Bearer {$apiKey}",
                        'Accept' => 'application/json',
                    ],
                    'query' => [
                        'no' => $user['appika_id']
                    ]
                ]);

                $searchData = json_decode($searchResult->getBody()->getContents(), true);

                // Log search result
                fwrite($log_file, date('Y-m-d H:i:s') . " - User ID: $user_id - Search Result: " . json_encode($searchData) . "\n");

                // If customer found in Appika
                if (isset($searchData['items']) && !empty($searchData['items'])) {
                    $customerData = $searchData['items'][0];
                    $customerDbId = $customerData['id'];

                    // Prepare customer data for update
                    $customerName = $edit_first_name . ' ' . $edit_last_name;
                    $customerStartDate = date('Y-m-d', strtotime($customerData['start_date']));

                    // Set fixed values as per requirements
                    $customerGroup = '10';
                    $ofcId = '511';
                    $assignTo = '1';
                    $creator = '1';
                    $status = 'a';

                    // Create customer data structure for Appika API update
                    $updateData = [
                        'no' => $user['appika_id'],
                        'name' => $customerName,
                        'entity_type' => '1', // 1 for COMPANY, 2 for INDIVIDUAL
                        'grp_id' => $customerGroup,
                        'ofc_id' => $ofcId,
                        'assign2' => $assignTo,
                        'creator' => $creator,
                        'start_date' => $customerStartDate,
                        'status' => $status
                    ];

                    // Update customer in Appika
                    $updatePath = $apiPath . '/' . $customerDbId;
                    $updateResult = sendToAppikaAPI($updateData, 'PUT', $updatePath);

                    // Log update result
                    fwrite($log_file, date('Y-m-d H:i:s') . " - User ID: $user_id - Customer Update Result: " . json_encode($updateResult) . "\n");

                    // Now check if there's a primary location to update
                    $locationsResult = $client->request('GET', $apiPath . '/' . $customerDbId . '/locations', [
                        'headers' => [
                            'Authorization' => "Bearer {$apiKey}",
                            'Accept' => 'application/json',
                        ]
                    ]);

                    $locationsData = json_decode($locationsResult->getBody()->getContents(), true);

                    // Log locations result
                    fwrite($log_file, date('Y-m-d H:i:s') . " - User ID: $user_id - Locations Result: " . json_encode($locationsData) . "\n");

                    // Find primary location
                    $primaryLocationId = null;
                    if (isset($locationsData['items']) && !empty($locationsData['items'])) {
                        foreach ($locationsData['items'] as $location) {
                            if (isset($location['is_primary_loc']) && $location['is_primary_loc'] === 'y') {
                                $primaryLocationId = $location['id'];
                                break;
                            }
                        }
                    }

                    // If primary location found, update it
                    if ($primaryLocationId) {
                        // Prepare location data
                        $locationData = [
                            'loc_code' => 'LOC-' . $user['appika_id'],
                            'loc_name' => $customerName . ' Location',
                            'add1' => $edit_address,
                            'add2' => $edit_address2,
                            'ccode' => $edit_country,
                            'state_code' => $edit_state,
                            'city' => $edit_city,
                            'status' => $status,
                            'is_primary_loc' => 'y',
                            'zip' => $edit_postal_code,
                            'parent_id' => 0
                        ];

                        // Update location in Appika
                        $locationResult = updateLocationInAppika($customerDbId, $primaryLocationId, $locationData);

                        // Log location update result
                        fwrite($log_file, date('Y-m-d H:i:s') . " - User ID: $user_id - Location Update Result: " . json_encode($locationResult) . "\n");
                    }
                }
            }

            // Close log file
            fclose($log_file);

            $message = "User information updated successfully";
            $message_type = 'success';

            // Refresh user data
            $user_result = mysqli_query($conn, $user_query);
            $user = mysqli_fetch_assoc($user_result);

            // If AJAX request, return JSON success
            if ($is_ajax) {
                $response = [
                    'success' => true,
                    'message' => $message,
                    'user' => $user
                ];

                header('Content-Type: application/json');
                echo json_encode($response);
                exit;
            }
        } else {
            $message = "Error updating user information: " . mysqli_error($conn);
            $message_type = 'danger';

            // If AJAX request, return JSON error
            if ($is_ajax) {
                $response = [
                    'success' => false,
                    'message' => $message
                ];

                header('Content-Type: application/json');
                echo json_encode($response);
                exit;
            }
        }
    } else {
        $message = implode("<br>", $errors);
        $message_type = 'danger';

        // If AJAX request, return JSON error
        if ($is_ajax) {
            $response = [
                'success' => false,
                'message' => $message
            ];

            header('Content-Type: application/json');
            echo json_encode($response);
            exit;
        }
    }
}

if (isset($_POST['add_tickets'])) {
    $ticket_type = mysqli_real_escape_string($conn, $_POST['ticket_type']);
    $ticket_amount = (int)$_POST['ticket_amount'];

    if ($ticket_amount > 0 && in_array($ticket_type, ['starter_tickets', 'premium_tickets', 'ultimate_tickets'])) {
        // Update user's ticket count
        $update_sql = "UPDATE user SET $ticket_type = $ticket_type + $ticket_amount WHERE id = $user_id";

        if (mysqli_query($conn, $update_sql)) {
            // Create log entry
            $ticket_type_clean = str_replace('_tickets', '', $ticket_type);
            // Display 'Business' for premium tickets in logs
            if ($ticket_type_clean == 'premium') {
                $ticket_type_clean = 'Business';
            }
            $description = "Admin added $ticket_amount $ticket_type_clean tickets";
            $log_sql = "INSERT INTO ticket_logs (action, description, user_id, ticket_type, amount, performed_by_admin_id, created_at)
                        VALUES ('success', '$description', $user_id, '$ticket_type_clean', '$ticket_amount', $admin_id, NOW())";
            mysqli_query($conn, $log_sql);

            $message = "Successfully added $ticket_amount $ticket_type_clean tickets to user {$user['username']}";
            $message_type = 'success';

            // Refresh user data
            $user_result = mysqli_query($conn, $user_query);
            $user = mysqli_fetch_assoc($user_result);

            // Refresh logs
            $logs_result = mysqli_query($conn, $logs_query);
        } else {
            $message = "Error adding tickets: " . mysqli_error($conn);
            $message_type = 'danger';
        }
    } else {
        $message = "Invalid ticket type or amount";
        $message_type = 'danger';
    }
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - User Detail</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap, fonts & icons -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Vendor stylesheets -->
    <link rel="stylesheet" href="../css/main.css">
    <style>
    body {
        background-color: #f8f9fa;
    }

    .admin-container {
        padding: 20px;
        max-width: 100%;
        overflow-x: hidden;
    }

    .admin-header {
        background-color: #fff;
        padding: 15px 20px;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        position: relative;
    }

    /* Responsive container */
    @media (max-width: 991px) {
        .admin-container {
            padding: 15px;
        }

        .admin-header {
            padding: 12px 15px;
        }
    }

    @media (max-width: 767px) {
        .admin-container {
            padding: 10px;
        }

        .admin-header {
            padding: 10px;
            flex-direction: column;
            align-items: flex-start;
        }

        .admin-header h1 {
            margin-bottom: 10px;
            font-size: 20px;
        }
    }

    .admin-header h1 {
        margin: 0;
        font-size: 24px;
        color: #333;
    }

    .admin-user {
        display: flex;
        align-items: center;
    }

    .admin-user span {
        margin-right: 10px;
    }

    .admin-sidebar {
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        padding: 20px;
        height: calc(100vh - 100px);
        overflow-y: auto;
    }

    .admin-sidebar ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .admin-sidebar ul li {
        margin-bottom: 10px;
    }

    .admin-sidebar ul li a {
        display: block;
        padding: 10px 15px;
        color: #333;
        text-decoration: none;
        border-radius: 5px;
        transition: all 0.3s;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .admin-sidebar ul li a:hover,
    .admin-sidebar ul li a.active {
        background-color: #473BF0;
        color: #fff;
    }

    .admin-sidebar ul li a i {
        margin-right: 10px;
        width: 20px;
        text-align: center;
    }

    /* Responsive sidebar */
    @media (max-width: 991px) {
        .admin-sidebar {
            padding: 15px;
            height: calc(100vh - 90px);
        }

        .admin-sidebar ul li a {
            padding: 8px 12px;
            font-size: 14px;
        }
    }

    @media (max-width: 767px) {
        .admin-sidebar {
            height: auto;
            margin-bottom: 15px;
        }

        .admin-sidebar ul {
            display: flex;
            flex-wrap: wrap;
        }

        .admin-sidebar ul li {
            margin-right: 5px;
            margin-bottom: 5px;
            width: calc(50% - 5px);
        }

        .admin-sidebar ul li a {
            padding: 8px 10px;
            font-size: 13px;
            text-align: center;
        }

        .admin-sidebar ul li a i {
            margin-right: 5px;
            width: auto;
        }
    }

    @media (max-width: 480px) {
        .admin-sidebar ul li {
            width: 100%;
            margin-right: 0;
        }
    }

    .admin-content {
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        padding: 20px;
        height: calc(100vh - 100px);
        overflow-y: auto;
    }

    .user-info-card {
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        padding: 20px;
        margin-bottom: 20px;
    }

    .user-info-card h3 {
        margin-top: 0;
        color: #333;
        border-bottom: 1px solid #eee;
        padding-bottom: 10px;
        margin-bottom: 15px;
        font-size: 18px;
    }

    .user-info-row {
        margin-bottom: 10px;
    }

    .user-info-label {
        font-weight: 600;
        color: #555;
    }

    /* Responsive content */
    @media (max-width: 991px) {
        .admin-content {
            padding: 15px;
            height: calc(100vh - 90px);
        }

        .user-info-card {
            padding: 15px;
            margin-bottom: 15px;
        }

        .user-info-card h3 {
            font-size: 16px;
            padding-bottom: 8px;
            margin-bottom: 12px;
        }
    }

    @media (max-width: 767px) {
        .admin-content {
            height: auto;
            padding: 12px;
        }

        .user-info-card {
            padding: 12px;
            margin-bottom: 12px;
        }

        .user-info-row {
            margin-bottom: 8px;
            font-size: 14px;
        }

        .user-info-label {
            font-size: 14px;
        }
    }

    @media (max-width: 480px) {
        .admin-content {
            padding: 10px;
        }

        .user-info-card {
            padding: 10px;
        }

        .user-info-row {
            font-size: 13px;
        }

        .user-info-label {
            font-size: 13px;
        }
    }

    .ticket-count {
        background-color: #f8f9fa;
        border-radius: 5px;
        padding: 15px;
        text-align: center;
        margin-bottom: 10px;
        transition: all 0.3s ease;
    }

    .ticket-count h4 {
        margin: 0;
        color: #333;
        font-size: 16px;
    }

    .ticket-count .number {
        font-size: 24px;
        font-weight: 600;
        color: #473BF0;
        margin: 5px 0;
    }

    .section-title {
        margin-top: 30px;
        margin-bottom: 15px;
        color: #333;
        border-bottom: 1px solid #eee;
        padding-bottom: 10px;
        font-size: 18px;
    }

    /* Responsive ticket count */
    @media (max-width: 991px) {
        .ticket-count {
            padding: 12px;
        }

        .ticket-count h4 {
            font-size: 15px;
        }

        .ticket-count .number {
            font-size: 22px;
        }

        .section-title {
            margin-top: 25px;
            margin-bottom: 12px;
            font-size: 17px;
        }
    }

    @media (max-width: 767px) {
        .ticket-count {
            padding: 10px;
        }

        .ticket-count h4 {
            font-size: 14px;
        }

        .ticket-count .number {
            font-size: 20px;
        }

        .section-title {
            margin-top: 20px;
            margin-bottom: 10px;
            font-size: 16px;
        }
    }

    @media (max-width: 480px) {
        .ticket-count {
            padding: 8px;
        }

        .ticket-count h4 {
            font-size: 13px;
        }

        .ticket-count .number {
            font-size: 18px;
        }

        .section-title {
            font-size: 15px;
        }
    }

    /* Badge styles */
    .badge {
        font-size: 14px;
        padding: 5px 8px;
        border-radius: 4px;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }

    /* Status badges */
    .badge-open {
        background-color: #4CAF50;
        color: #fff !important;
    }

    .badge-in_progress {
        background-color: #2196F3;
        color: #fff !important;
    }

    .badge-resolved {
        background-color: #9C27B0;
        color: #fff !important;
    }

    .badge-closed {
        background-color: #757575;
        color: #fff !important;
    }

    /* Ticket type badges */
    .badge-starter,
    .badge-STARTER {
        background-color: #fbbf24;
        color: #fff !important;
    }

    .badge-premium,
    .badge-PREMIUM,
    .badge-business,
    .badge-BUSINESS {
        background-color: #01A7E1;
        color: #fff !important;
    }

    .badge-ultimate,
    .badge-ULTIMATE {
        background-color: #793BF0;
        color: #fff !important;
    }

    /* Action badges */
    .badge-success {
        background-color: #28a745;
        color: #fff !important;
    }

    .badge-pending {
        background-color: #fd7e14;
        color: #fff !important;
    }

    .badge-fail {
        background-color: #dc3545;
        color: #fff !important;
    }

    .badge-cancel {
        background-color: #6c757d;
        color: #fff !important;
    }

    /* Priority badges */
    .badge-low {
        background-color: #6c757d;
        color: #fff !important;
    }

    .badge-medium {
        background-color: #fd7e14;
        color: #fff !important;
    }

    .badge-high {
        background-color: #dc3545;
        color: #fff !important;
    }

    .badge-critical {
        background-color: #9c27b0;
        color: #fff !important;
    }

    /* Responsive badge styles */
    @media (max-width: 767px) {
        .badge {
            font-size: 12px;
            padding: 4px 6px;
        }
    }

    @media (max-width: 480px) {
        .badge {
            font-size: 11px;
            padding: 3px 5px;
        }
    }

    /* Button styles */
    .btn {
        font-size: 14px;
        font-weight: 500;
        padding: 8px 16px;
        border-radius: 5px;
        transition: all 0.3s ease;
    }

    .btn-sm {
        padding: 5px 10px;
        font-size: 12px;
    }

    .btn-primary {
        background-color: #473BF0;
        border-color: #473BF0;
    }

    .btn-primary:hover {
        background-color: #3730c0;
        border-color: #3730c0;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        color: #fff !important;
    }

    .btn-outline-primary {
        color: #473BF0;
        border-color: #473BF0;
    }

    .btn-outline-primary:hover {
        background-color: #473BF0;
        color: white !important;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .btn-outline-danger {
        color: #dc3545;
        border-color: #dc3545;
    }

    .btn-outline-danger:hover {
        background-color: #dc3545;
        color: white !important;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    /* View button styles */
    a.btn.btn-primary.view-btn {
        width: 40px !important;
        height: 40px !important;
        min-width: 40px !important;
        max-width: 40px !important;
        min-height: 40px !important;
        max-height: 40px !important;
        padding: 0 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        font-size: 10px !important;
        border-radius: 4px !important;
        text-align: center !important;
        line-height: 1 !important;
        overflow: hidden !important;
        white-space: nowrap !important;
    }

    /* Responsive button styles */
    @media (max-width: 767px) {
        .btn {
            padding: 6px 12px;
            font-size: 13px;
        }

        .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
        }

        a.btn.btn-primary.view-btn {
            width: 35px !important;
            height: 35px !important;
            min-width: 35px !important;
            max-width: 35px !important;
            min-height: 35px !important;
            max-height: 35px !important;
            font-size: 12px !important;
        }
    }

    @media (max-width: 480px) {
        .btn {
            padding: 5px 10px;
            font-size: 12px;
        }

        .btn-sm {
            padding: 3px 6px;
            font-size: 11px;
        }

        a.btn.btn-primary.view-btn {
            width: 30px !important;
            height: 30px !important;
            min-width: 30px !important;
            max-width: 30px !important;
            min-height: 30px !important;
            max-height: 30px !important;
            font-size: 10px !important;
        }
    }

    /* Modal styles */
    .modal-header {
        background-color: #473BF0;
        color: white;
        border-top-left-radius: 5px;
        border-top-right-radius: 5px;
    }

    .modal-header .close {
        color: white;
        opacity: 0.8;
    }

    .modal-header .close:hover {
        opacity: 1;
    }

    .modal-title {
        font-size: 22px;
        color: white;
        font-weight: 500;
    }

    .modal-body .form-group label {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        margin-bottom: 8px;
    }

    @media (max-width: 767px) {
        .modal-body .form-group label {
            font-size: 14px;
        }
    }

    /* Modal button styles - matching admin-ticket-detail.php */
    .modal-btn-primary {
        background-color: #473BF0 !important;
        color: white !important;
        border: none !important;
        padding: 8px 16px !important;
        font-size: 14px !important;
        border-radius: 4px !important;
        transition: all 0.3s ease !important;
        min-width: 80px;
    }

    .modal-btn-primary:hover {
        background-color: #3a30c0 !important;
    }

    .modal-btn-cancel {
        background-color: #dc3545 !important;
        color: white !important;
        border: none !important;
        padding: 8px 16px !important;
        font-size: 14px !important;
        border-radius: 4px !important;
        transition: all 0.3s ease !important;
        min-width: 80px;
    }

    .modal-btn-cancel:hover {
        background-color: #c82333 !important;
    }

    @media (max-width: 767px) {

        .modal-btn-primary,
        .modal-btn-cancel {
            font-size: 13px !important;
            padding: 7px 14px !important;
        }
    }

    @media (max-width: 575px) {

        .modal-btn-primary,
        .modal-btn-cancel {
            font-size: 12px !important;
            padding: 6px 12px !important;
        }
    }

    .modal-footer .btn {
        min-width: 80px;
    }

    /* Table styles */
    .table {
        width: 100%;
        margin-bottom: 1rem;
        color: #212529;
        border-collapse: collapse;
    }

    .table th,
    .table td {
        padding: 0.75rem;
        vertical-align: middle;
        border-top: 1px solid #dee2e6;
    }

    .table thead th {
        vertical-align: bottom;
        border-bottom: 2px solid #dee2e6;
        background-color: #f8f9fa;
        font-weight: 600;
    }

    .table-striped tbody tr:nth-of-type(odd) {
        background-color: rgba(0, 0, 0, 0.03);
    }

    .table-responsive {
        display: block;
        width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        margin-bottom: 20px;
    }

    /* Table styles for mobile */
    @media (max-width: 767px) {
        .table {
            font-size: 14px;
        }

        .table th,
        .table td {
            padding: 0.5rem 0.3rem;
        }
    }

    @media (max-width: 480px) {
        .table {
            font-size: 13px;
        }

        .table th,
        .table td {
            padding: 0.4rem 0.2rem;
        }
    }

    /* User dropdown styles */
    .user-dropdown {
        position: relative;
        display: inline-block;
    }

    .user-info {
        cursor: pointer;
        display: flex;
        align-items: center;
    }

    .mobile-menu-toggle {
        margin-left: 5px;
        transition: transform 0.3s ease;
        display: none;
    }

    .mobile-menu-toggle.active {
        transform: rotate(180deg);
    }

    .dropdown-content {
        display: block;
    }

    /* Mobile Styles for dropdown */
    @media (max-width: 767px) {
        .admin-user {
            width: 100%;
        }

        .user-dropdown {
            width: 100%;
        }

        .user-info {
            width: 100%;
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
        }

        .mobile-menu-toggle {
            display: inline-block;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            background-color: #f9f9f9;
            width: 100%;
            left: 0;
            box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
            z-index: 1;
            border-radius: 4px;
            margin-top: 5px;
            padding: 8px;
        }

        .dropdown-content .btn {
            width: 100%;
            text-align: center;
        }

        .user-dropdown.active .dropdown-content {
            display: block;
        }
    }
    </style>
</head>

<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1>User Detail: <?php echo htmlspecialchars($user['username']); ?></h1>
            <div class="admin-user">
                <div class="user-dropdown">
                    <span class="user-info">
                        Welcome, <?php echo htmlspecialchars($admin_username); ?>
                        (<?php echo htmlspecialchars($admin_role); ?>)
                        <i class="mobile-menu-toggle fas fa-chevron-down"></i>
                    </span>
                    <div class="dropdown-content">
                        <a href="admin-logout.php" class="btn btn-sm btn-outline-danger">Logout</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-2 col-md-3 col-sm-12">
                <?php include('admin-menu.php'); ?>
            </div>

            <div class="col-lg-10 col-md-9 col-sm-12">
                <div class="admin-content">
                    <?php if (!empty($message)): ?>
                    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                        <?php echo $message; ?>
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <?php endif; ?>

                    <div class="row">
                        <div class="col-lg-8 col-md-7 col-sm-12">
                            <div class="user-info-card">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h3 class="mb-0">User Information</h3>
                                    <button type="button" class="btn btn-sm btn-primary" data-toggle="modal"
                                        data-target="#editUserModal">
                                        <i class="fas fa-edit"></i>&nbsp; Edit
                                    </button>
                                </div>
                                <hr class="mt-2 mb-3">
                                <div class="row">
                                    <div class="col-lg-6 col-md-12 col-sm-12">
                                        <div class="user-info-row">
                                            <div class="user-info-label">Appika ID:</div>
                                            <div><?php echo htmlspecialchars($user['appika_id'] ?? 'N/A'); ?></div>
                                        </div>
                                        <div class="user-info-row">
                                            <div class="user-info-label">First Name:</div>
                                            <div><?php echo htmlspecialchars($user['first_name'] ?? 'N/A'); ?></div>
                                        </div>
                                        <div class="user-info-row">
                                            <div class="user-info-label">Last Name:</div>
                                            <div><?php echo htmlspecialchars($user['last_name'] ?? 'N/A'); ?></div>
                                        </div>
                                        <div class="user-info-row">
                                            <div class="user-info-label">Username:</div>
                                            <div><?php echo htmlspecialchars($user['username']); ?></div>
                                        </div>
                                        <div class="user-info-row">
                                            <div class="user-info-label">Email:</div>
                                            <div><?php echo htmlspecialchars($user['email']); ?></div>
                                        </div>
                                        <div class="user-info-row">
                                            <div class="user-info-label">Phone:</div>
                                            <div><?php echo htmlspecialchars($user['tell'] ?? 'N/A'); ?></div>
                                        </div>
                                        <div class="user-info-row">
                                            <div class="user-info-label">Company:</div>
                                            <div><?php echo htmlspecialchars($user['company_name'] ?? 'N/A'); ?></div>
                                        </div>
                                        <div class="user-info-row">
                                            <div class="user-info-label">Tax ID:</div>
                                            <div><?php echo htmlspecialchars($user['tax_id'] ?? 'N/A'); ?></div>
                                        </div>
                                    </div>
                                    <div class="col-lg-6 col-md-12 col-sm-12">
                                        <div class="user-info-row">
                                            <div class="user-info-label">Address:</div>
                                            <div><?php echo htmlspecialchars($user['address'] ?? 'N/A'); ?></div>
                                        </div>
                                        <div class="user-info-row">
                                            <div class="user-info-label">Address Notes:</div>
                                            <div><?php echo htmlspecialchars($user['address2'] ?? 'N/A'); ?></div>
                                        </div>
                                        <div class="user-info-row">
                                            <div class="user-info-label">District:</div>
                                            <div><?php echo htmlspecialchars($user['district'] ?? 'N/A'); ?></div>
                                        </div>
                                        <div class="user-info-row">
                                            <div class="user-info-label">City:</div>
                                            <div><?php echo htmlspecialchars($user['city'] ?? 'N/A'); ?></div>
                                        </div>
                                        <div class="user-info-row">
                                            <div class="user-info-label">State/Province:</div>
                                            <div><?php echo htmlspecialchars($user['state'] ?? 'N/A'); ?></div>
                                        </div>
                                        <div class="user-info-row">
                                            <div class="user-info-label">Postal Code:</div>
                                            <div><?php echo htmlspecialchars($user['postal_code'] ?? 'N/A'); ?></div>
                                        </div>
                                        <div class="user-info-row">
                                            <div class="user-info-label">Country:</div>
                                            <div><?php echo htmlspecialchars($user['country'] ?? 'N/A'); ?></div>
                                        </div>
                                        <div class="user-info-row">
                                            <div class="user-info-label">Registration Date:</div>
                                            <div><?php echo date('Y-m-d', strtotime($user['registration_time'])); ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4 col-md-5 col-sm-12">
                            <div class="user-info-card">
                                <h3>Ticket Balance</h3>
                                <div class="row">
                                    <div class="col-lg-4 col-md-4 col-sm-4 col-4">
                                        <div class="ticket-count">
                                            <h4>Starter</h4>
                                            <div class="number"><?php echo $user['starter_tickets']; ?></div>
                                        </div>
                                    </div>
                                    <div class="col-lg-4 col-md-4 col-sm-4 col-4">
                                        <div class="ticket-count">
                                            <h4>Premium</h4>
                                            <div class="number"><?php echo $user['premium_tickets']; ?></div>
                                        </div>
                                    </div>
                                    <div class="col-lg-4 col-md-4 col-sm-4 col-4">
                                        <div class="ticket-count">
                                            <h4>Ultimate</h4>
                                            <div class="number"><?php echo $user['ultimate_tickets']; ?></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-center mt-3">
                                    <button type="button" class="btn btn-primary" data-toggle="modal"
                                        data-target="#addTicketsModal">
                                        Add Tickets
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <h3 class="section-title">Recent Tickets</h3>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Subject</th>
                                    <th>Type</th>
                                    <th>Status</th>
                                    <th>Priority</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (mysqli_num_rows($tickets_result) > 0): ?>
                                <?php while ($ticket = mysqli_fetch_assoc($tickets_result)): ?>
                                <tr>
                                    <td><?php echo $ticket['id']; ?></td>
                                    <td><?php echo htmlspecialchars($ticket['subject']); ?></td>
                                    <td>
                                        <span class="badge badge-<?php echo $ticket['ticket_type']; ?>">
                                            <?php echo ucfirst($ticket['ticket_type']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-<?php echo $ticket['status']; ?>">
                                            <?php echo ucfirst($ticket['status']); ?>
                                        </span>
                                    </td>
                                    <td><?php echo ucfirst($ticket['priority']); ?></td>
                                    <td><?php echo date('Y-m-d', strtotime($ticket['created_at'])); ?></td>
                                    <td>
                                        <a href="admin-ticket-detail.php?id=<?php echo $ticket['id']; ?>"
                                            class="btn btn-sm btn-primary">View</a>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                                <?php else: ?>
                                <tr>
                                    <td colspan="7" class="text-center">No tickets found</td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                        <div class="text-right">
                            <a href="admin-tickets.php?search=<?php echo urlencode($user['username']); ?>"
                                class="btn btn-outline-primary">View All Tickets</a>
                        </div>
                    </div>

                    <h3 class="section-title">Recent Purchases</h3>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Ticket Type</th>
                                    <th>Package Size</th>
                                    <th>Quantity</th>
                                    <th>Price</th>
                                    <th>Remaining</th>
                                    <th>Purchase Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (mysqli_num_rows($purchases_result) > 0): ?>
                                <?php while ($purchase = mysqli_fetch_assoc($purchases_result)): ?>
                                <tr>
                                    <td><?php echo $purchase['purchaseid']; ?></td>
                                    <td>
                                        <span class="badge badge-<?php echo $purchase['ticket_type']; ?>">
                                            <?php echo $purchase['ticket_type']; ?>
                                        </span>
                                    </td>
                                    <td><?php echo htmlspecialchars($purchase['package_size']); ?></td>
                                    <td><?php echo $purchase['numbers_per_package']; ?></td>
                                    <td>$<?php echo number_format($purchase['dollar_price_per_package'], 2); ?></td>
                                    <td><?php echo $purchase['remaining_tickets']; ?></td>
                                    <td><?php echo date('Y-m-d', strtotime($purchase['purchase_time'])); ?></td>
                                </tr>
                                <?php endwhile; ?>
                                <?php else: ?>
                                <tr>
                                    <td colspan="7" class="text-center">No purchases found</td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                        <div class="text-right">
                            <a href="admin-purchases.php?search=<?php echo urlencode($user['username']); ?>"
                                class="btn btn-outline-primary">View All Purchases</a>
                        </div>
                    </div>

                    <h3 class="section-title">Recent Ticket Logs</h3>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Action</th>
                                    <th>Type</th>
                                    <th>Amount</th>
                                    <th>Description</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (mysqli_num_rows($logs_result) > 0): ?>
                                <?php while ($log = mysqli_fetch_assoc($logs_result)): ?>
                                <tr>
                                    <td><?php echo $log['id']; ?></td>
                                    <td>
                                        <span class="badge badge-<?php echo $log['action']; ?>">
                                            <?php echo ucfirst($log['action']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if (!empty($log['ticket_type'])): ?>
                                        <span class="badge badge-<?php echo $log['ticket_type']; ?>">
                                            <?php echo ucfirst($log['ticket_type']); ?>
                                        </span>
                                        <?php else: ?>
                                        -
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo $log['amount'] ?? '-'; ?></td>
                                    <td><?php echo htmlspecialchars($log['description']); ?></td>
                                    <td><?php echo formatTimeForDisplay($log['created_at'], 'Asia/Singapore', 'Y-m-d H:i'); ?>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                                <?php else: ?>
                                <tr>
                                    <td colspan="6" class="text-center">No logs found</td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                        <div class="text-right">
                            <a href="admin-ticket-logs.php?search=<?php echo urlencode($user['username']); ?>"
                                class="btn btn-outline-primary">View All Logs</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit User Modal -->
    <div class="modal fade" id="editUserModal" tabindex="-1" role="dialog" aria-labelledby="editUserModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editUserModalLabel">Edit User:
                        <?php echo htmlspecialchars($user['username']); ?></h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form id="editUserForm" method="POST">
                    <div class="modal-body">
                        <div class="alert alert-danger mt-3 edit-error-message" style="display: none;"></div>

                        <input type="hidden" name="edit_user" value="1">
                        <input type="hidden" name="ajax" value="1">

                        <div class="form-group">
                            <label for="edit_first_name">First Name</label>
                            <input type="text" class="form-control" id="edit_first_name" name="edit_first_name"
                                value="<?php echo htmlspecialchars($user['first_name'] ?? ''); ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="edit_last_name">Last Name</label>
                            <input type="text" class="form-control" id="edit_last_name" name="edit_last_name"
                                value="<?php echo htmlspecialchars($user['last_name'] ?? ''); ?>" required>
                        </div>

                        <div class="form-group">
                            <label>Username</label>
                            <input type="text" class="form-control"
                                value="<?php echo htmlspecialchars($user['username']); ?>" readonly>
                            <small class="form-text text-muted">Username cannot be changed</small>
                        </div>

                        <div class="form-group">
                            <label for="edit_email">Email</label>
                            <input type="email" class="form-control" id="edit_email" name="edit_email"
                                value="<?php echo htmlspecialchars($user['email']); ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="edit_phone">Phone</label>
                            <input type="text" class="form-control" id="edit_phone" name="edit_phone"
                                value="<?php echo htmlspecialchars($user['tell'] ?? ''); ?>">
                        </div>

                        <div class="form-group">
                            <label for="edit_company">Company</label>
                            <input type="text" class="form-control" id="edit_company" name="edit_company"
                                value="<?php echo htmlspecialchars($user['company_name'] ?? ''); ?>">
                        </div>

                        <div class="form-group">
                            <label for="edit_tax_id">Tax ID</label>
                            <input type="text" class="form-control" id="edit_tax_id" name="edit_tax_id"
                                value="<?php echo htmlspecialchars($user['tax_id'] ?? ''); ?>">
                        </div>

                        <div class="form-group">
                            <label for="edit_address">Address</label>
                            <textarea class="form-control" id="edit_address" name="edit_address" rows="2"
                                placeholder="Enter the primary address"><?php echo htmlspecialchars($user['address'] ?? ''); ?></textarea>
                        </div>

                        <div class="form-group">
                            <label for="edit_address2">Address Notes (Optional)</label>
                            <textarea class="form-control" id="edit_address2" name="edit_address2" rows="3"
                                placeholder="Enter any additional notes about the address"><?php echo htmlspecialchars($user['address2'] ?? ''); ?></textarea>
                        </div>

                        <div class="form-group">
                            <label for="edit_district">District</label>
                            <input type="text" class="form-control" id="edit_district" name="edit_district"
                                value="<?php echo htmlspecialchars($user['district'] ?? ''); ?>">
                        </div>

                        <div class="form-group">
                            <label for="edit_city">City</label>
                            <input type="text" class="form-control" id="edit_city" name="edit_city"
                                value="<?php echo htmlspecialchars($user['city'] ?? ''); ?>">
                        </div>

                        <div class="form-group">
                            <label for="edit_state">State/Province</label>
                            <input type="text" class="form-control" id="edit_state" name="edit_state"
                                value="<?php echo htmlspecialchars($user['state'] ?? ''); ?>">
                        </div>

                        <div class="form-group">
                            <label for="edit_postal_code">Postal Code</label>
                            <input type="text" class="form-control" id="edit_postal_code" name="edit_postal_code"
                                value="<?php echo htmlspecialchars($user['postal_code'] ?? ''); ?>">
                        </div>

                        <div class="form-group">
                            <label for="edit_country">Country</label>
                            <input type="text" class="form-control" id="edit_country" name="edit_country"
                                value="<?php echo htmlspecialchars($user['country'] ?? ''); ?>">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn modal-btn-cancel" data-dismiss="modal">Cancel</button>
                        <button type="button" id="editUserBtn" class="btn modal-btn-primary">Save Changes</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Add Tickets Modal -->
    <div class="modal fade" id="addTicketsModal" tabindex="-1" role="dialog" aria-labelledby="addTicketsModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addTicketsModalLabel">Add Tickets for
                        <?php echo htmlspecialchars($user['username']); ?></h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="ticket_type">Ticket Type</label>
                            <select class="form-control" id="ticket_type" name="ticket_type" required>
                                <option value="starter_tickets">Starter</option>
                                <option value="premium_tickets">Business</option>
                                <option value="ultimate_tickets">Ultimate</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="ticket_amount">Amount</label>
                            <input type="number" class="form-control" id="ticket_amount" name="ticket_amount" min="1"
                                value="1" required>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn modal-btn-cancel" data-dismiss="modal">Cancel</button>
                        <button type="submit" name="add_tickets" class="btn modal-btn-primary">Add Tickets</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/vendor.min.js"></script>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const userDropdown = document.querySelector('.user-dropdown');
        const userInfo = document.querySelector('.user-info');
        const menuToggle = document.querySelector('.mobile-menu-toggle');

        // Only apply dropdown functionality on mobile
        if (window.innerWidth <= 767) {
            userInfo.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                userDropdown.classList.toggle('active');
                menuToggle.classList.toggle('active');
            });

            // Also add direct click handler to the toggle icon for better mobile experience
            menuToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                userDropdown.classList.toggle('active');
                menuToggle.classList.toggle('active');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(event) {
                if (!userDropdown.contains(event.target)) {
                    userDropdown.classList.remove('active');
                    menuToggle.classList.remove('active');
                }
            });
        }

        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth > 767) {
                userDropdown.classList.remove('active');
                menuToggle.classList.remove('active');
            }
        });

        // Handle user edit via AJAX
        const editUserBtn = document.getElementById('editUserBtn');
        if (editUserBtn) {
            editUserBtn.addEventListener('click', function() {
                const form = document.getElementById('editUserForm');
                const errorMessage = form.querySelector('.edit-error-message');
                const modal = jQuery('#editUserModal');

                // Validate form
                if (!form.checkValidity()) {
                    form.reportValidity();
                    return;
                }

                // Show loading state
                this.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> Saving...';
                this.disabled = true;

                // Create form data
                const formData = new FormData(form);

                // Send AJAX request
                fetch(window.location.href, {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Success - update the user information in the page
                            updateUserInfo(data.user);

                            // Close the modal
                            modal.modal('hide');

                            // Show success message
                            showNotification(data.message, 'success');
                        } else {
                            // Error
                            errorMessage.textContent = data.message;
                            errorMessage.style.display = 'block';

                            // Reset button
                            this.innerHTML = 'Save Changes';
                            this.disabled = false;
                        }
                    })
                    .catch(error => {
                        // Network or other error
                        errorMessage.textContent =
                            'An error occurred while processing your request. Please try again.';
                        errorMessage.style.display = 'block';

                        // Reset button
                        this.innerHTML = 'Save Changes';
                        this.disabled = false;

                        console.error('Error:', error);
                    });
            });
        }

        // Function to update user information in the page
        function updateUserInfo(user) {
            // Update first name
            const firstNameElement = document.querySelector('.user-info-row:nth-child(1) div:nth-child(2)');
            if (firstNameElement) {
                firstNameElement.textContent = user.first_name || 'N/A';
            }

            // Update last name
            const lastNameElement = document.querySelector('.user-info-row:nth-child(2) div:nth-child(2)');
            if (lastNameElement) {
                lastNameElement.textContent = user.last_name || 'N/A';
            }

            // Update email
            const emailElement = document.querySelector('.user-info-row:nth-child(4) div:nth-child(2)');
            if (emailElement) {
                emailElement.textContent = user.email;
            }

            // Update phone
            const phoneElement = document.querySelector('.user-info-row:nth-child(5) div:nth-child(2)');
            if (phoneElement) {
                phoneElement.textContent = user.tell || 'N/A';
            }

            // Update company
            const companyElement = document.querySelector('.user-info-row:nth-child(6) div:nth-child(2)');
            if (companyElement) {
                companyElement.textContent = user.company_name || 'N/A';
            }

            // Update tax ID
            const taxIdElement = document.querySelector('.user-info-row:nth-child(7) div:nth-child(2)');
            if (taxIdElement) {
                taxIdElement.textContent = user.tax_id || 'N/A';
            }

            // Update address
            const addressElement = document.querySelector(
                '.col-lg-6.col-md-12.col-sm-12:nth-child(2) .user-info-row:nth-child(1) div:nth-child(2)');
            if (addressElement) {
                addressElement.textContent = user.address || 'N/A';
            }

            // Update address2
            const address2Element = document.querySelector(
                '.col-lg-6.col-md-12.col-sm-12:nth-child(2) .user-info-row:nth-child(2) div:nth-child(2)');
            if (address2Element) {
                address2Element.textContent = user.address2 || 'N/A';
            }

            // Update district
            const districtElement = document.querySelector(
                '.col-lg-6.col-md-12.col-sm-12:nth-child(2) .user-info-row:nth-child(3) div:nth-child(2)');
            if (districtElement) {
                districtElement.textContent = user.district || 'N/A';
            }

            // Update city
            const cityElement = document.querySelector(
                '.col-lg-6.col-md-12.col-sm-12:nth-child(2) .user-info-row:nth-child(4) div:nth-child(2)');
            if (cityElement) {
                cityElement.textContent = user.city || 'N/A';
            }

            // Update state
            const stateElement = document.querySelector(
                '.col-lg-6.col-md-12.col-sm-12:nth-child(2) .user-info-row:nth-child(5) div:nth-child(2)');
            if (stateElement) {
                stateElement.textContent = user.state || 'N/A';
            }

            // Update postal code
            const postalCodeElement = document.querySelector(
                '.col-lg-6.col-md-12.col-sm-12:nth-child(2) .user-info-row:nth-child(6) div:nth-child(2)');
            if (postalCodeElement) {
                postalCodeElement.textContent = user.postal_code || 'N/A';
            }

            // Update country
            const countryElement = document.querySelector(
                '.col-lg-6.col-md-12.col-sm-12:nth-child(2) .user-info-row:nth-child(7) div:nth-child(2)');
            if (countryElement) {
                countryElement.textContent = user.country || 'N/A';
            }
        }

        // Function to show notification
        function showNotification(message, type, duration = 5000) {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = 'alert alert-' + type + ' alert-dismissible fade show notification-toast';
            notification.innerHTML = message +
                '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
                '<span aria-hidden="true">&times;</span></button>';

            // Style the notification
            notification.style.position = 'fixed';
            notification.style.top = '20px';
            notification.style.right = '20px';
            notification.style.zIndex = '9999';
            notification.style.minWidth = '300px';
            notification.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';

            // Add to document
            document.body.appendChild(notification);

            // Auto remove after specified duration
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 150);
            }, duration);
        }
    });
    </script>
</body>

</html>