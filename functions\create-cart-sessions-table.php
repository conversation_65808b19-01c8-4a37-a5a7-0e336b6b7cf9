<?php
include('server.php');

echo "<h2>🔧 Create Cart Sessions Table</h2>";

// Create cart_sessions table to store cart data for webhook processing
$create_table_sql = "
CREATE TABLE IF NOT EXISTS cart_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(255) UNIQUE NOT NULL,
    cart_data TEXT NOT NULL,
    user_id INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP NULL,
    INDEX idx_session_id (session_id),
    INDEX idx_created_at (created_at)
)
";

if (mysqli_query($conn, $create_table_sql)) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #c3e6cb;'>";
    echo "<h4 style='color: #155724;'>✅ Success!</h4>";
    echo "<p style='color: #155724;'>Cart sessions table created successfully.</p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #f5c6cb;'>";
    echo "<h4 style='color: #721c24;'>❌ Error</h4>";
    echo "<p style='color: #721c24;'>Error creating table: " . mysqli_error($conn) . "</p>";
    echo "</div>";
}

echo "<h3>📋 Table Structure:</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<ul>";
echo "<li><strong>session_id:</strong> Unique identifier for cart session</li>";
echo "<li><strong>cart_data:</strong> JSON data of all cart items</li>";
echo "<li><strong>user_id:</strong> User ID (NULL for guests)</li>";
echo "<li><strong>created_at:</strong> When cart session was created</li>";
echo "<li><strong>processed_at:</strong> When webhook processed the cart</li>";
echo "</ul>";
echo "</div>";

echo "<h3>🔗 Next Steps:</h3>";
echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<p>Now the system can:</p>";
echo "<ul>";
echo "<li>✅ Store unlimited cart items in database</li>";
echo "<li>✅ Pass only a small session reference to Stripe metadata</li>";
echo "<li>✅ Webhook retrieves full cart data from database</li>";
echo "<li>✅ No more 500 character limit issues</li>";
echo "</ul>";
echo "</div>";

echo "<div style='display: flex; gap: 10px; flex-wrap: wrap; margin: 20px 0;'>";
echo "<a href='../front-end/cart.php' style='background: #007cba; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Test Cart</a>";
echo "<a href='debug-cart-purchase-issue.php' style='background: #6c757d; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Debug Tool</a>";
echo "</div>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2, h3, h4, h5 { color: #333; }
</style>
