<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Auto-detect environment for file paths
$is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
$file_base_path = $is_localhost ? '/helloit' : '';

try {
    require_once $_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/vendor/autoload.php';
    include($_SERVER['DOCUMENT_ROOT'] . $file_base_path . '/functions/server.php');
    session_start();
} catch (Exception $e) {
    die("Error loading files: " . $e->getMessage());
}

// Set your Stripe API keys
\Stripe\Stripe::setApiKey('sk_test_51ROUofEJRyUMDOj5Op3iqq0gpje1mQqwhINGCYVwVRvmOixzZh1IUmfwArcDt7FtJkN9aszopPP6dyxVWyOZeC8F00gwZtB0ju');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        header('location: ' . $file_base_path . '/front-end/sign-in.php');
    } else {
        header('Content-Type: application/json');
        echo json_encode(['error' => 'User not logged in']);
    }
    exit();
}

$user_id = $_SESSION['user_id'];

// Check if this is a GET request from the payment method selection page
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $cart_id = isset($_GET['cart_id']) ? $_GET['cart_id'] : null;
    $payment_method = isset($_GET['payment_method']) ? $_GET['payment_method'] : null;
    $new_card = isset($_GET['new_card']) ? (bool)$_GET['new_card'] : false;
    $make_default = isset($_GET['make_default']) ? (bool)$_GET['make_default'] : true; // Default to true
    $save_payment_method = isset($_GET['save_payment_method']) ? (bool)$_GET['save_payment_method'] : true; // Default to save
} else {
    // This is a POST request (AJAX from cart page)
    header('Content-Type: application/json');
    $data = json_decode(file_get_contents('php://input'), true);
    $cart_id = isset($data['cart_id']) ? $data['cart_id'] : null;
    $save_payment_method = isset($data['save_payment_method']) ? (bool)$data['save_payment_method'] : true; // Default to save
}

// Validate cart_id
if (!$cart_id) {
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        header('location: ' . $file_base_path . '/front-end/cart.php');
    } else {
        header('Content-Type: application/json');
        echo json_encode(['error' => 'Invalid cart ID']);
    }
    exit();
}

// Sanitize inputs to prevent SQL injection
$cart_id = mysqli_real_escape_string($conn, $cart_id);

// Get cart items
$query = "SELECT ci.id AS cart_item_id, ci.ticket_id, ci.quantity, t.ticket_type, t.package_size, t.dollar_price_per_package, t.numbers_per_package
          FROM cart_items ci
          JOIN tickets t ON ci.ticket_id = t.ticketid
          WHERE ci.cart_id = ?";

$stmt = $conn->prepare($query);
$stmt->bind_param("i", $cart_id);
$stmt->execute();
$result = $stmt->get_result();
$cart_items = $result->fetch_all(MYSQLI_ASSOC);

if (empty($cart_items)) {
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        header('location: ' . $file_base_path . '/front-end/cart.php?error=empty');
    } else {
        header('Content-Type: application/json');
        echo json_encode(['error' => 'Cart is empty']);
    }
    exit();
}

// Prepare line items for Stripe
$line_items = [];
$metadata = [];
$i = 0;

foreach ($cart_items as $item) {
    $price = $item['dollar_price_per_package'] * 100; // Convert to cents for Stripe

    // Create product name and description
    $product_name = $item['ticket_type'] . ' ' . $item['package_size'];
    $tickets_text = $item['numbers_per_package'] == 1 ? 'ticket' : 'tickets';
    $description = $item['numbers_per_package'] . ' ' . $tickets_text;

    $line_items[] = [
        'price_data' => [
            'currency' => 'usd',
            'unit_amount' => $price,
            'product_data' => [
                'name' => $product_name,
                'description' => $description,
            ]
        ],
        'quantity' => $item['quantity']
    ];

    // Store cart item data for webhook processing
    $cart_metadata[] = [
        'ticket_type' => $item['ticket_type'],
        'package_size' => $item['package_size'],
        'numbers_per_package' => $item['numbers_per_package'],
        'dollar_price_per_package' => $item['dollar_price_per_package'],
        'quantity' => $item['quantity']
    ];

    // Add metadata for each item (backup method)
    if ($i < 10) { // Limit to 10 items in metadata to avoid exceeding Stripe's limit
        $metadata['item_' . $i . '_ticket_id'] = $item['ticket_id'];
        $metadata['item_' . $i . '_ticket_type'] = $item['ticket_type'];
        $metadata['item_' . $i . '_package_size'] = $item['package_size'];
        $metadata['item_' . $i . '_numbers_per_package'] = $item['numbers_per_package'];
        $metadata['item_' . $i . '_quantity'] = $item['quantity'];
        $i++;
    }
}

// Store cart data in database to avoid Stripe 500 character limit
$cart_session_id = 'cart_' . uniqid() . '_' . time();
$cart_data_json = json_encode($cart_metadata);

// Insert cart data into database
$insert_cart_query = "INSERT INTO cart_sessions (session_id, cart_data, user_id) VALUES (?, ?, ?)";
$cart_stmt = $conn->prepare($insert_cart_query);
$cart_stmt->bind_param("ssi", $cart_session_id, $cart_data_json, $user_id);
$cart_stmt->execute();

// Add minimal metadata
$metadata['cart_id'] = $cart_id;
$metadata['total_items'] = count($cart_metadata);
$metadata['cart_session_id'] = $cart_session_id; // Reference to database
$metadata['total_amount'] = array_sum(array_column($cart_metadata, 'dollar_price_per_package'));
$metadata['user_id'] = $user_id;
$metadata['username'] = isset($_SESSION['username']) ? $_SESSION['username'] : '';
$metadata['make_default'] = $make_default ? 'true' : 'false';
$metadata['save_payment_method'] = $save_payment_method ? '1' : '0';

// Use environment-aware base path
$base_path = $file_base_path;

// Get user email and stripe_customer_id from database
$user_email = '';
$stripe_customer_id = '';
$user_query = $conn->prepare("SELECT email, stripe_customer_id FROM user WHERE id = ?");
$user_query->bind_param("i", $user_id);
$user_query->execute();
$user_query->bind_result($user_email, $stripe_customer_id);
$user_query->fetch();
$user_query->close();

// If no Stripe customer ID, create one and save it
if (!$stripe_customer_id) {
    $customer = \Stripe\Customer::create([
        'email' => $user_email
    ]);
    $stripe_customer_id = $customer->id;
    $update = $conn->prepare("UPDATE user SET stripe_customer_id = ? WHERE id = ?");
    $update->bind_param("si", $stripe_customer_id, $user_id);
    $update->execute();
    $update->close();
}

// Create a Stripe Checkout session or PaymentIntent
try {
    // If using a saved payment method, create a PaymentIntent directly
    if (isset($payment_method) && $payment_method && strpos($payment_method, 'pm_') === 0) {
        // Calculate total amount
        $amount = 0;
        foreach ($line_items as $item) {
            $amount += $item['price_data']['unit_amount'] * $item['quantity'];
        }

        // Create a PaymentIntent with the saved payment method
        $payment_intent = \Stripe\PaymentIntent::create([
            'amount' => $amount,
            'currency' => 'usd',
            'customer' => $stripe_customer_id,
            'payment_method' => $payment_method,
            'off_session' => false, // This is an on-session payment
            'confirm' => true, // Confirm the payment immediately
            'metadata' => $metadata,
            'return_url' => (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]{$base_path}/functions/cart-payment-success.php?payment_intent_id={PAYMENT_INTENT_ID}",
            'confirmation_method' => 'automatic',
        ]);

        // Check if payment requires additional action
        if ($payment_intent->status === 'requires_action' && $payment_intent->next_action->type === 'redirect_to_url') {
            // Redirect to the URL provided by Stripe for 3D Secure authentication
            header('location: ' . $payment_intent->next_action->redirect_to_url->url);
            exit();
        } else if ($payment_intent->status === 'succeeded') {
            // Payment succeeded, redirect to success page
            header('location: ' . $file_base_path . '/functions/cart-payment-success.php?payment_intent_id=' . $payment_intent->id);
            exit();
        } else {
            // Payment failed or requires further action
            header('location: ' . $file_base_path . '/front-end/cart.php?error=Payment failed or requires further action. Status: ' . $payment_intent->status);
            exit();
        }
    } else {
        // Check if user already has 2 or more payment methods
        try {
            $existing_payment_methods = \Stripe\PaymentMethod::all([
                'customer' => $stripe_customer_id,
                'type' => 'card',
            ]);

            if (count($existing_payment_methods->data) >= 2) {
                // User has reached the maximum number of cards
                if ($_SERVER['REQUEST_METHOD'] === 'GET') {
                    header('location: ' . $file_base_path . '/front-end/select-payment-method.php?cart_id=' . $cart_id . '&error=' . urlencode('You have reached the maximum number of saved cards (2). Please remove an existing card before adding a new one.'));
                    exit();
                } else {
                    header('Content-Type: application/json');
                    echo json_encode(['error' => 'Maximum number of cards reached']);
                    exit();
                }
            }
        } catch (Exception $e) {
            // If there's an error checking payment methods, continue with checkout
            error_log("Error checking payment methods: " . $e->getMessage());
        }

        // Using a new card - create a Checkout Session
        $session_params = [
            'customer' => $stripe_customer_id,
            'line_items' => $line_items,
            'mode' => 'payment',
            'success_url' => (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]{$base_path}/functions/cart-payment-success.php?session_id={CHECKOUT_SESSION_ID}",
            'cancel_url' => (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]{$base_path}/front-end/cancel.php?canceled=true",
            'metadata' => $metadata,
            'payment_method_types' => ['card'],
            'billing_address_collection' => 'required'
        ];

        // Only save payment method if checkbox was checked
        if ($save_payment_method) {
            $session_params['payment_intent_data'] = [
                'setup_future_usage' => 'off_session', // This will save the card for future use
            ];
        }

        $checkout_session = \Stripe\Checkout\Session::create($session_params);

        // Handle response based on request type
        if ($_SERVER['REQUEST_METHOD'] === 'GET') {
            // Redirect to Stripe Checkout
            header("location: " . $checkout_session->url);
            exit();
        } else {
            // Return session ID as JSON response for AJAX
            header('Content-Type: application/json');
            echo json_encode(['sessionId' => $checkout_session->id]);
        }
    }
} catch (Exception $e) {
    // Handle exception based on request type
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        header('location: ' . $file_base_path . '/front-end/cart.php?error=' . urlencode($e->getMessage()));
    } else {
        header('Content-Type: application/json');
        echo json_encode(['error' => $e->getMessage()]);
    }
}
?>