<?php
// Include database connection
include('../functions/server.php');

// Include timezone helper for proper time handling
include('../functions/timezone-helper.php');

// Check if user is logged in
session_start();
if (!isset($_SESSION['user_id'])) {
    header('location: login.php');
    exit();
}

$user_id = $_SESSION['user_id'];
$username = $_SESSION['username'];

// Get ticket ID if provided
$ticket_id = isset($_GET['ticket_id']) ? intval($_GET['ticket_id']) : null;
$ticket_info = null;

// If ticket ID is provided, get ticket information
if ($ticket_id) {
    $ticket_query = "SELECT * FROM support_tickets WHERE id = $ticket_id AND user_id = $user_id";
    $ticket_result = mysqli_query($conn, $ticket_query);

    if ($ticket_result && mysqli_num_rows($ticket_result) > 0) {
        $ticket_info = mysqli_fetch_assoc($ticket_result);
    } else {
        // If ticket doesn't exist or doesn't belong to the user, redirect to tickets page
        header('location: my-ticket.php');
        exit();
    }
}

// Create chat_messages table if it doesn't exist
$create_table_sql = "CREATE TABLE IF NOT EXISTS chat_messages (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    ticket_id INT(11) NOT NULL,
    sender_id INT(11) NOT NULL,
    sender_type ENUM('user', 'admin') NOT NULL,
    message TEXT NOT NULL,
    is_read TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);";

mysqli_query($conn, $create_table_sql);

// Handle sending new messages
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['message']) && !empty($_POST['message']) && $ticket_id) {
    // Check if ticket is closed or resolved
    if ($ticket_info['status'] === 'closed' || $ticket_info['status'] === 'resolved') {
        // Redirect with error message
        header("Location: chat-support.php?ticket_id=$ticket_id&error=ticket_closed");
        exit();
    }

    $message = $_POST['message'];

    // Get current UTC time for consistent storage
    $utc_time = UTCTimeHelper::getCurrentUTC();

    $insert_query = "INSERT INTO chat_messages (ticket_id, sender_id, sender_type, message, created_at)
                    VALUES (?, ?, 'user', ?, ?)";
    $stmt = mysqli_prepare($conn, $insert_query);
    mysqli_stmt_bind_param($stmt, 'iiss', $ticket_id, $user_id, $message, $utc_time);
    $success = mysqli_stmt_execute($stmt);
    mysqli_stmt_close($stmt);

    if ($success) {
        // admin_notifications table removed - notifications handled by chat_messages.is_read system
    }

    // Redirect to prevent form resubmission
    header("Location: chat-support.php?ticket_id=$ticket_id");
    exit();
}

// Get chat messages for the selected ticket
$messages = [];
if ($ticket_id) {
    $messages_query = "SELECT cm.*,
                      CASE
                          WHEN cm.sender_type = 'admin' THEN a.username
                          WHEN cm.sender_type = 'user' THEN u.username
                      END as sender_name
                      FROM chat_messages cm
                      LEFT JOIN admin_users a ON cm.sender_id = a.id AND cm.sender_type = 'admin'
                      LEFT JOIN user u ON cm.sender_id = u.id AND cm.sender_type = 'user'
                      WHERE cm.ticket_id = $ticket_id
                      ORDER BY cm.created_at ASC";
    $messages_result = mysqli_query($conn, $messages_query);

    if ($messages_result) {
        while ($message = mysqli_fetch_assoc($messages_result)) {
            $messages[] = $message;
        }
    }

    // Mark admin messages as read
    $update_query = "UPDATE chat_messages
                    SET is_read = 1
                    WHERE ticket_id = $ticket_id
                    AND sender_type = 'admin'
                    AND is_read = 0";
    mysqli_query($conn, $update_query);
}

// Get user information
$user_query = "SELECT * FROM user WHERE id = $user_id";
$user_result = mysqli_query($conn, $user_query);
$user = mysqli_fetch_assoc($user_result);

// Count remaining tickets
$starter_query = "SELECT SUM(remaining_tickets) as starter_remaining FROM purchasetickets WHERE username = '{$username}' AND ticket_type = 'starter'";
$premium_query = "SELECT SUM(remaining_tickets) as premium_remaining FROM purchasetickets WHERE username = '{$username}' AND ticket_type = 'premium'";
$ultimate_query = "SELECT SUM(remaining_tickets) as ultimate_remaining FROM purchasetickets WHERE username = '{$username}' AND ticket_type = 'ultimate'";

$starter_result = mysqli_query($conn, $starter_query);
$premium_result = mysqli_query($conn, $premium_query);
$ultimate_result = mysqli_query($conn, $ultimate_query);

$starterRemaining = mysqli_fetch_assoc($starter_result)['starter_remaining'] ?: 0;
$premiumRemaining = mysqli_fetch_assoc($premium_result)['premium_remaining'] ?: 0;
$ultimateRemaining = mysqli_fetch_assoc($ultimate_result)['ultimate_remaining'] ?: 0;
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live Chat Support</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap , fonts & icons  -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Vendor stylesheets  -->
    <link rel="stylesheet" href="../css/main.css">

    <!-- Customer timezone handling -->
    <script src="../js/customer-timezone.js"></script>
    <style>
    .notification-badge {
        text-align: center !important;
    }

    body {
        padding-top: 200px;
        background-color: #F4F7FA;
    }

    body {
        margin-top: -75px !important;

    }


    @media (max-width: 767px) {
        body {
            margin-top: -100px !important;
        }

        .badge {
            font-size: 14px !important;

        }

        .col-12.d-md-none.mb-4 {
            padding-left: 40px;
            padding-right: 40px;
        }
    }

    .chat-container {
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        padding: 0;
        margin-bottom: 30px;
        overflow: hidden;
        height: 600px;
        display: flex;
        flex-direction: column;
    }

    .ticket-info {
        background-color: #f8f9fa;
        border-radius: 5px;
        padding: 15px;
        margin-bottom: 20px;
    }

    /* Container width to match my-ticket.php */
    .container {
        width: 1800px;
        max-width: 95%;
    }

    /* Make site-wrapper wider */
    .site-wrapper {
        max-width: 100% !important;
        width: 100% !important;
        overflow-x: visible !important;
    }

    .ticket-info h3 {
        margin-top: 0;
        color: #333;
    }

    .ticket-meta {
        display: flex;
        flex-wrap: wrap;
        margin-top: 10px;
    }

    .ticket-meta-item {
        margin-right: 20px;
        margin-bottom: 5px;
    }

    .ticket-meta-label {
        font-weight: 600;
        color: #555;
        margin-right: 5px;
    }

    .badge {
        font-size: 16px;
        padding: 6.8px;
    }

    .badge-starter {
        background-color: #fbbf24;
        color: #fff !important;
    }

    .badge-premium,
    .badge-business {
        background-color: #01A7E1;
        color: #fff !important;
    }

    .badge-ultimate {
        background-color: #793BF0;
        color: #fff !important;
    }

    .badge-open {
        background-color: #4CAF50;
        color: #fff;
    }

    .badge-in_progress {
        background-color: #2196F3;
        color: #fff;
    }

    .badge-resolved {
        background-color: #9C27B0;
        color: #fff;
    }

    .badge-closed {
        background-color: #757575;
        color: #fff;
    }

    /* Chat styles */
    .chat-header {
        padding: 15px;
        background-color: #f8f9fa;
        border-bottom: 1px solid #e5e5e5;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    /* Mobile-specific chat header styles */
    @media (max-width: 767px) {
        .chat-header {
            padding: 10px;
        }

        .chat-title {
            font-size: 16px;
        }

        .chat-badges .badge {
            font-size: 12px !important;
            padding: 4px 6px !important;
        }
    }

    /* Extra small devices */
    @media (max-width: 480px) {
        .chat-header {
            flex-wrap: wrap;
        }

        .chat-header .d-flex.align-items-center {
            width: 100%;
            margin-bottom: 8px;
        }

        .chat-badges {
            width: 100%;
            justify-content: flex-start;
        }

        .chat-container {
            height: 450px;
            /* Even smaller for very small screens */
        }

        .chat-input {
            padding: 10px;
        }

        .chat-input input,
        .chat-input textarea {
            padding: 8px 10px;
        }

        .chat-input button {
            padding: 8px 12px;
            font-size: 13px;
            margin-left: 5px;
        }
    }

    .chat-header h4 {
        margin: 0;
        font-size: 18px;
        color: #333;
    }

    .chat-messages {
        flex: 1;
        padding: 15px;
        overflow-y: auto;
        background-color: #f8f9fa;
    }

    .chat-message {
        margin-bottom: 15px;
        display: flex;
        flex-direction: column;
    }

    .chat-message-content {
        max-width: 80%;
        padding: 10px 15px;
        border-radius: 18px;
        position: relative;
        word-wrap: break-word;
    }

    .chat-message-time {
        font-size: 11px;
        color: #6c757d;
        margin-top: 5px;
    }

    .chat-message.outgoing {
        align-items: flex-end;
    }

    .chat-message.incoming {
        align-items: flex-start;
    }

    .chat-message.outgoing .chat-message-content {
        background-color: #473BF0;
        color: white;
        border-bottom-right-radius: 5px;
    }

    .chat-message.incoming .chat-message-content {
        background-color: #e9ecef;
        color: #212529;
        border-bottom-left-radius: 5px;
    }

    .chat-input {
        padding: 15px;
        border-top: 1px solid #e5e5e5;
        background-color: #fff;
        display: flex;
        align-items: center;
    }

    .chat-input form {
        display: flex;
        width: 100%;
        align-items: center;
    }

    .chat-input input,
    .chat-input textarea {
        flex: 1;
        padding: 10px 15px;
        border: 1px solid #ced4da;
        border-radius: 30px;
        outline: none;
        font-family: inherit;
        font-size: inherit;
        resize: none;
        min-height: 40px;
        max-height: 120px;
        overflow-y: auto;
    }

    .chat-input button {
        margin-left: 10px;
        border: none;
        background-color: #473BF0;
        color: white;
        border-radius: 30px;
        padding: 10px 20px;
        cursor: pointer;
        transition: background-color 0.2s;
        align-self: center;
        height: 40px;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .chat-input button:hover {
        background-color: #3730c0;
    }

    .chat-empty {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: #6c757d;
        text-align: center;
        padding: 20px;
    }

    .chat-empty i {
        font-size: 48px;
        margin-bottom: 15px;
        color: #dee2e6;
    }

    .select-ticket-container {
        padding: 20px;
    }

    .notification-badge {
        background-color: #ff3b30;
        color: white;
        border-radius: 50%;
        min-width: 20px;
        height: 20px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        padding: 0 5px;
        vertical-align: middle;
        margin-left: 5px;
        float: right;
    }

    /* Position absolute for the back button notification */
    .position-absolute.notification-badge {
        margin-left: 0;
    }

    /* Back button container */
    .back-btn-container {
        position: relative;
        width: 40px;
        height: 40px;
        margin-right: 15px;
        flex-shrink: 0;
        display: inline-block;
    }

    /* Back button size */
    .back-button {
        width: 40px !important;
        height: 40px !important;
        min-width: 40px !important;
        max-width: 40px !important;
        min-height: 40px !important;
        max-height: 40px !important;
        padding: 0 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        border-radius: 4px !important;
        box-sizing: border-box !important;
        overflow: visible !important;
        text-align: center !important;
        margin: 0 !important;
        position: relative;
        z-index: 1;
    }

    /* Chat title styles */
    .chat-title {
        margin: 0;
        font-size: 18px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* Chat badges container */
    .chat-badges {
        display: flex;
        align-items: center;
        flex-wrap: nowrap;
        gap: 5px;
    }


    h5.mb-1 {
        font-size: 16px !important;
    }

    /* Add these styles to make ONLY ticket list full width on mobile */
    @media (max-width: 767px) {

        /* Chat container adjustments for mobile */
        .chat-container {
            height: 500px;
            /* Slightly smaller on mobile */
        }

        .chat-input input,
        .chat-input textarea {
            font-size: 14px;
        }

        .chat-input button {
            padding: 8px 15px;
            font-size: 14px;
        }

        /* Target only the ticket list container */
        .select-ticket-container .list-group-item {
            padding: 15px 10px;
            width: 100%;
        }

        .select-ticket-container {
            padding: 10px;
            width: 100%;
        }

        .select-ticket-container .list-group {
            width: 100%;
            margin: 0;
            padding: 0;
        }

        .select-ticket-container .list-group-item h5.mb-1 {
            font-size: 14px !important;
            width: 100%;
            white-space: normal;
            word-break: break-word;
        }

        .select-ticket-container .list-group-item .text-muted {
            font-size: 12px;
        }

        /* Make container full width on mobile */
        .container {
            width: 100% !important;
            max-width: 100% !important;
            padding-left: 5px !important;
            padding-right: 5px !important;
        }

        /* Make the main content area full width */
        .col-lg-9,
        .col-md-8 {
            padding-left: 0 !important;
            padding-right: 0 !important;
        }

        /* Make the white background card full width */
        .bg-white.p-8 {
            padding: 10px !important;
            border-radius: 0 !important;
        }

        /* Adjust the ticket list to be full width */
        .select-ticket-container .list-group {
            margin-left: -10px !important;
            margin-right: -10px !important;
            width: calc(100% + 20px) !important;
        }

        /* Preserve the original width for the account menu */
        .dashboard-menu {
            width: auto !important;
            max-width: 100% !important;
        }

        .dashboard-menu .list-group-item {
            width: auto !important;
        }

        .dashboard-menu .menu-items {
            width: auto !important;
        }
    }

    /* Fix for hamburger icon positioning */
    @media (max-width: 767px) {
        .hamburger-menu {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1050;
        }

        .hamburger-icon {
            position: relative;
            width: 24px;
            height: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        /* Ensure the hamburger icon stays in place */
        .nav-header {
            position: relative;
        }

        /* Prevent the header from pushing the hamburger icon */
        .header-main {
            padding-right: 50px;
        }
    }

    /* Specific styles to preserve account menu width */
    @media (max-width: 767px) {

        /* Override any full-width styles for the dashboard menu */
        .col-12.d-md-none.mb-4 .dashboard-menu,
        .col-lg-3.col-md-4.d-none.d-md-block.mb-4 .dashboard-menu {
            width: auto !important;
            margin-right: 0 !important;
            margin-left: 0 !important;
        }

        /* Ensure menu items are not affected by the ticket list styles */
        .dashboard-menu .list-unstyled.menu-items li {
            width: auto !important;
        }

        .dashboard-menu .list-unstyled.menu-items li a {
            width: auto !important;
        }

        /* Make sure the dashboard menu doesn't expand to full width */
        .dashboard-menu ul.menu-items.expanded {
            width: auto !important;
            max-width: 100% !important;
        }
    }

    .notification-badge.position-absolute {
        position: absolute !important;
        top: -6px !important;
        /* right: -6px !important; */
        z-index: 10 !important;
        background: #dc3545 !important;
        color: #fff !important;
        border-radius: 50% !important;
        min-width: 22px !important;
        height: 22px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        font-size: 13px !important;
        font-weight: bold !important;
        /* box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1) !important; */
        padding: 0 6px !important;
        /* border: 2px solid #fff !important; */
    }
    </style>
</head>

<body>
    <?php include('../header-footer/header.php'); ?>

    <div class="container">
        <!-- Mobile heading - visible only on mobile -->
        <!-- <div class="row d-md-none">
            <div class="col-12 text-center mb-3">
                <h2 class="mb-4">Live Chat Support</h2>
            </div>
        </div> -->

        <div class="row">
            <!-- Sidebar - Hidden on mobile, visible on larger screens -->
            <div class="col-lg-3 col-md-4 d-none d-md-block mb-4">
                <?php include('user-menu.php'); ?>
            </div>

            <!-- Mobile menu - Visible only on mobile -->
            <div class="col-12 d-md-none mb-4">
                <?php include('user-menu.php'); ?>
            </div>

            <!-- Main content -->
            <div class="col-lg-9 col-md-8">
                <div class="bg-white p-8 rounded-10 mb-5 overflow-hidden position-relative">
                    <h2 class="text-center mb-4">Live Chat Support</h2>

                    <?php if (isset($_GET['error']) && $_GET['error'] === 'ticket_closed'): ?>
                    <div class="alert alert-warning alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        <strong>Cannot send message:</strong> This ticket is closed or resolved. You cannot send new
                        messages.
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <?php endif; ?>

                    <?php if ($ticket_info): ?>
                    <div class="cart-details-main-block" id="dynamic-cart">
                        <!-- White background card with rounded corners -->
                        <div class="bg-white p-8 rounded-10 mb-5 overflow-hidden position-relative">
                            <div class="ticket-info">
                                <h3>Ticket #<?php echo $ticket_id; ?>:
                                    <?php echo htmlspecialchars($ticket_info['subject']); ?></h3>
                                <div class="ticket-meta">
                                    <div class="ticket-meta-item">
                                        <span class="ticket-meta-label">Status:</span>
                                        <span
                                            class="badge badge-<?php echo $ticket_info['status']; ?>"><?php echo ucfirst($ticket_info['status']); ?></span>
                                    </div>
                                    <div class="ticket-meta-item">
                                        <span class="ticket-meta-label">Type:</span>
                                        <?php
                                        // Display 'Business' for premium tickets
                                        $ticketType = $ticket_info['ticket_type'];
                                        $badgeClass = $ticketType;
                                        $displayText = ucfirst($ticketType);

                                        if (strtolower($ticketType) == 'premium') {
                                            $displayText = 'Business';
                                        }
                                        ?>
                                        <span
                                            class="badge badge-<?php echo $badgeClass; ?>"><?php echo $displayText; ?></span>
                                    </div>

                                    <div class="ticket-meta-item">

                                        <span class="ticket-meta-label"> Created:</span>
                                        <span><?php echo showCustomerTime($ticket_info['created_at'], 'M j, Y g:i A'); ?></span>
                                    </div>
                                    <div class="ticket-meta-item">
                                        <a href="ticket-detail.php?id=<?php echo $ticket_id; ?>"
                                            class="btn btn-sm btn-outline-primary">View Ticket Details</a>
                                    </div>
                                </div>
                            </div>

                            <div class="chat-container">
                                <!-- Chat Header -->
                                <div class="chat-header">
                                    <div class="d-flex align-items-center">
                                        <?php
                                        // Count total unread messages across all tickets
                                        $unread_query = "SELECT SUM(unread_count) as total_unread FROM (
                                                        SELECT COUNT(*) as unread_count
                                                        FROM chat_messages
                                                        WHERE ticket_id IN (SELECT id FROM support_tickets WHERE user_id = $user_id)
                                                        AND sender_type = 'admin'
                                                        AND is_read = 0
                                                        GROUP BY ticket_id
                                                      ) as counts";
                                        $unread_result = mysqli_query($conn, $unread_query);
                                        $unread_data = mysqli_fetch_assoc($unread_result);
                                        $total_unread = $unread_data['total_unread'] ?: 0;
                                        ?>
                                        <div class="back-btn-container" style="position: relative;">
                                            <a href="chat-support.php"
                                                class="btn btn-outline-primary position-relative back-button">
                                                <i class="fas fa-arrow-left"></i>
                                            </a>
                                            <?php if ($total_unread > 0): ?>
                                            <span id="back-button-notification"
                                                class="notification-badge position-absolute"
                                                style="top: -8px; right: -8px; z-index: 2;">
                                                <?php echo $total_unread >= 10 ? '9+' : $total_unread; ?>
                                            </span>
                                            <?php endif; ?>
                                        </div>
                                        <h4 class="chat-title">Chat with Support Team</h4>
                                    </div>
                                    <div class="chat-badges">
                                        <span
                                            class="badge badge-<?php echo $ticket_info['status']; ?>"><?php echo ucfirst($ticket_info['status']); ?></span>
                                        <span
                                            class="badge badge-<?php echo $badgeClass; ?>"><?php echo $displayText; ?></span>
                                    </div>
                                </div>

                                <!-- Chat Messages -->
                                <div class="chat-messages" id="chatMessages">
                                    <?php if (empty($messages)): ?>
                                    <div class="chat-empty">
                                        <i class="fas fa-comments"></i>
                                        <p>No messages yet. Start the conversation by sending a message below.</p>
                                    </div>
                                    <?php else: ?>
                                    <?php foreach ($messages as $message): ?>
                                    <div
                                        class="chat-message <?php echo $message['sender_type'] == 'user' ? 'outgoing' : 'incoming'; ?>">
                                        <div class="chat-message-content">
                                            <?php echo nl2br(htmlspecialchars($message['message'])); ?>
                                        </div>
                                        <div class="chat-message-time">
                                            <?php echo showCustomerTime($message['created_at'], 'M d, g:i A'); ?> -
                                            <?php echo htmlspecialchars($message['sender_name'] ?? ($message['sender_type'] == 'admin' ? 'Support Team' : $username)); ?>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                    <?php endif; ?>
                                </div>

                                <!-- Chat Input -->
                                <div class="chat-input">
                                    <?php if ($ticket_info['status'] === 'closed' || $ticket_info['status'] === 'resolved'): ?>
                                    <div class="alert alert-secondary mb-0 text-center">
                                        <i class="fas fa-lock mr-2"></i> This ticket has been
                                        <?php echo $ticket_info['status']; ?>. Thank you for using our service 😊
                                    </div>
                                    <?php else: ?>
                                    <form id="chatForm" onsubmit="sendMessage(event)">
                                        <input type="hidden" id="ticket_id" value="<?php echo $ticket_id; ?>">
                                        <input type="hidden" id="ticket_status"
                                            value="<?php echo $ticket_info['status']; ?>">
                                        <textarea id="message" placeholder="Type a message... (Shift+Enter for new line)" required
                                            autocomplete="off" rows="1" onkeydown="handleMessageKeydown(event)"
                                            oninput="autoResizeTextarea(this)"></textarea>
                                        <button type="submit">
                                            <i class="fas fa-paper-plane"></i> &nbsp; Send
                                        </button>
                                    </form>
                                    <?php endif; ?>
                                </div>
                            </div>

                        </div>
                    </div>
                    <?php else: ?>
                    <div class="cart-details-main-block" id="dynamic-cart">
                        <!-- White background card with rounded corners -->
                        <div class="bg-white p-8 rounded-10 mb-5 overflow-hidden position-relative">
                            <div class="select-ticket-container">
                                <div class="alert alert-info">
                                    <p>Please select a ticket to start chatting with our support team. You can view your
                                        tickets on
                                        the <a href="my-ticket.php">My Tickets</a> page.</p>
                                </div>

                                <?php
                                // Pagination settings
                                $itemsPerPage = 5;
                                $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
                                if ($page < 1) $page = 1;
                                $offset = ($page - 1) * $itemsPerPage;

                                // Count total tickets for pagination
                                $countSql = "SELECT COUNT(*) as total FROM support_tickets WHERE user_id = $user_id";
                                $countResult = mysqli_query($conn, $countSql);
                                $totalItems = mysqli_fetch_assoc($countResult)['total'];
                                $totalPages = ceil($totalItems / $itemsPerPage);

                                // Get user's tickets with unread message counts and pagination
                                $tickets_query = "SELECT st.*,
                                                  (SELECT COUNT(*) FROM chat_messages cm
                                                   WHERE cm.ticket_id = st.id
                                                   AND cm.sender_type = 'admin'
                                                   AND cm.is_read = 0) as unread_count
                                                  FROM support_tickets st
                                                  WHERE st.user_id = $user_id
                                                  ORDER BY st.created_at DESC
                                                  LIMIT $itemsPerPage OFFSET $offset";
                                $tickets_result = mysqli_query($conn, $tickets_query);

                                if (mysqli_num_rows($tickets_result) > 0):
                                ?>
                                <h4>Your Tickets</h4>

                                <div class="list-group mt-3">
                                    <?php while ($ticket = mysqli_fetch_assoc($tickets_result)): ?>
                                    <a href="chat-support.php?ticket_id=<?php echo $ticket['id']; ?>"
                                        class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                        <div>
                                            <h5 class="mb-1">#<?php echo $ticket['id']; ?>:
                                                <?php echo htmlspecialchars($ticket['subject']); ?></h5>
                                            <small class="text-muted">
                                                <?php
            // Display 'Business' for premium tickets
            $ticketType = $ticket['ticket_type'];
            $badgeClass = $ticketType;
            $displayText = ucfirst($ticketType);

            if (strtolower($ticketType) == 'premium') {
                $displayText = 'Business';
            }
            ?>
                                                <span
                                                    class="badge badge-<?php echo $badgeClass; ?>"><?php echo $displayText; ?></span>
                                                <span
                                                    class="badge badge-<?php echo $ticket['status']; ?>"><?php echo ucfirst($ticket['status']); ?></span>
                                                &nbsp; Created: <?php echo showCustomerTime($ticket['created_at'], 'M j, Y g:i A'); ?>
                                            </small>
                                        </div>
                                        <div>
                                            <?php if ($ticket['unread_count'] > 0): ?>
                                            <span id="notification-ticket-<?php echo $ticket['id']; ?>"
                                                class="notification-badge">
                                                <?php echo $ticket['unread_count'] >= 10 ? '9+' : $ticket['unread_count']; ?>
                                            </span>
                                            <?php endif; ?>
                                        </div>
                                    </a>
                                    <?php endwhile; ?>
                                </div>

                                <!-- Pagination Controls -->
                                <?php if ($totalPages > 1): ?>
                                <div class="pagination-container mt-4">
                                    <nav aria-label="Page navigation">
                                        <ul class="pagination justify-content-center">
                                            <!-- Previous Button -->
                                            <li class="page-item <?php echo ($page <= 1) ? 'disabled' : ''; ?>">
                                                <a class="page-link" href="?page=<?php echo $page - 1; ?>"
                                                    aria-label="Previous">
                                                    <span aria-hidden="true">&laquo; Previous</span>
                                                </a>
                                            </li>

                                            <!-- Page Numbers -->
                                            <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                            <li class="page-item <?php echo ($page == $i) ? 'active' : ''; ?>">
                                                <a class="page-link" href="?page=<?php echo $i; ?>">
                                                    <?php echo $i; ?>
                                                </a>
                                            </li>
                                            <?php endfor; ?>

                                            <!-- Next Button -->
                                            <li
                                                class="page-item <?php echo ($page >= $totalPages) ? 'disabled' : ''; ?>">
                                                <a class="page-link" href="?page=<?php echo $page + 1; ?>"
                                                    aria-label="Next">
                                                    <span aria-hidden="true">Next &raquo;</span>
                                                </a>
                                            </li>
                                        </ul>
                                    </nav>
                                </div>
                                <?php endif; ?>

                                <?php else: ?>
                                <div class="alert alert-warning">
                                    <p>You don't have any tickets yet. <a href="create-ticket.php"
                                            class="alert-link">Create
                                            a new
                                            ticket</a> to get support.</p>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Scripts -->
        <script src="../js/vendor.min.js"></script>
        <script src="../plugins/theme-mode-switcher/gr-theme-mode-switcher.js"></script>
        <script src="../js/chat-notifications.js"></script>

        <script>
        // Scroll to bottom of chat messages
        function scrollToBottom() {
            const chatMessages = document.getElementById('chatMessages');
            if (chatMessages) {
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }
        }

        // Get user's timezone from PHP
        const userTimezone = '<?php echo getCustomerTimezone(); ?>';

        // Format message time in user's specific timezone
        function formatMessageTime(utcTimeString) {
            try {
                // Parse UTC time and convert to user's specific timezone
                const utcDate = new Date(utcTimeString + 'Z'); // Add Z to indicate UTC

                // Format in user's specific timezone (not browser's local timezone)
                return utcDate.toLocaleString('en-US', {
                    timeZone: userTimezone,
                    month: 'short',
                    day: 'numeric',
                    hour: 'numeric',
                    minute: '2-digit',
                    hour12: true
                });
            } catch (error) {
                console.error('Time formatting error:', error);
                return utcTimeString;
            }
        }

        // Format message HTML
        function formatMessage(message) {
            const messageType = message.sender_type == 'user' ? 'outgoing' : 'incoming';
            const senderName = message.sender_name || (message.sender_type == 'admin' ? 'Support Team' :
                '<?php echo $username; ?>');
            // Use server-formatted time if available (from PHP with proper timezone), otherwise format in JS
            const time = message.formatted_time || formatMessageTime(message.created_at);

            return `
                <div class="chat-message ${messageType}">
                    <div class="chat-message-content">
                        ${message.message.replace(/\n/g, '<br>')}
                    </div>
                    <div class="chat-message-time">
                        ${time} - ${senderName}
                    </div>
                </div>
            `;
        }

        // Get current messages count
        let currentMessagesCount = <?php echo count($messages); ?>;

        // Handle Shift+Enter for line breaks and Enter for sending
        function handleMessageKeydown(event) {
            if (event.key === 'Enter') {
                if (event.shiftKey) {
                    // Shift+Enter: Allow line break (default behavior)
                    return true;
                } else {
                    // Enter: Send message
                    event.preventDefault();
                    sendMessage(event);
                    return false;
                }
            }
        }

        // Auto-resize textarea based on content
        function autoResizeTextarea(textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px'; // Max height of 120px
        }

        // Function to update ticket status in real-time
        function updateTicketStatus(newStatus) {
            console.log('Updating ticket status from', document.getElementById('ticket_status').value, 'to', newStatus);

            // Update hidden input
            document.getElementById('ticket_status').value = newStatus;

            // Update all status badges
            const statusBadges = document.querySelectorAll('.badge-open, .badge-in_progress, .badge-resolved, .badge-closed');
            statusBadges.forEach(badge => {
                // Remove all status classes
                badge.classList.remove('badge-open', 'badge-in_progress', 'badge-resolved', 'badge-closed');
                // Add new status class
                badge.classList.add('badge-' + newStatus);
                // Update text
                badge.textContent = newStatus.charAt(0).toUpperCase() + newStatus.slice(1).replace('_', ' ');
            });

            // Update chat input based on status
            const chatInput = document.querySelector('.chat-input');
            if (newStatus === 'closed' || newStatus === 'resolved') {
                // Replace chat input with disabled message
                chatInput.innerHTML = `
                    <div class="alert alert-secondary mb-0 text-center">
                        <i class="fas fa-lock mr-2"></i> This ticket has been ${newStatus}. Thank you for using our service 😊.
                    </div>
                `;
            } else {
                // Ensure chat input is enabled (in case it was previously disabled)
                const form = chatInput.querySelector('form');
                if (!form) {
                    // Restore the form if it was replaced
                    chatInput.innerHTML = `
                        <form id="chatForm" onsubmit="sendMessage(event)">
                            <input type="hidden" id="ticket_id" value="<?php echo $ticket_id; ?>">
                            <input type="hidden" id="ticket_status" value="${newStatus}">
                            <textarea id="message" placeholder="Type a message... (Shift+Enter for new line)" required
                                autocomplete="off" rows="1" onkeydown="handleMessageKeydown(event)"
                                oninput="autoResizeTextarea(this)"></textarea>
                            <button type="submit">
                                <i class="fas fa-paper-plane"></i> &nbsp; Send
                            </button>
                        </form>
                    `;
                }
            }

            // Show notification about status change
            if (window.chatNotificationSystem) {
                window.chatNotificationSystem.notify(
                    `Ticket status updated to: ${newStatus.charAt(0).toUpperCase() + newStatus.slice(1).replace('_', ' ')}`, {
                        title: 'Ticket Status Updated',
                        sender: 'Support Team'
                    }
                );
            }
        }

        // Function to send message via AJAX
        function sendMessage(event) {
            event.preventDefault();

            const messageInput = document.getElementById('message');
            const message = messageInput.value.trim();
            const ticketId = document.getElementById('ticket_id').value;
            const ticketStatus = document.getElementById('ticket_status').value;

            if (message === '') return;

            // Check if ticket is closed or resolved
            if (ticketStatus === 'closed' || ticketStatus === 'resolved') {
                alert('This ticket has been ' + ticketStatus + '. Thank you for using our service 😊.');
                return;
            }

            // Create form data
            const formData = new FormData();
            formData.append('message', message);
            formData.append('ticket_id', ticketId);

            // Send message via AJAX
            fetch('../support-ticket/send-message', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Clear input field and reset height
                        messageInput.value = '';
                        messageInput.style.height = 'auto';

                        // Add new message to chat
                        const chatMessages = document.getElementById('chatMessages');
                        const messageHTML = formatMessage(data.message);

                        // Check if chat is empty (has the "No messages yet" placeholder)
                        const chatEmpty = chatMessages.querySelector('.chat-empty');
                        if (chatEmpty) {
                            // Replace the empty state with the first message
                            chatMessages.innerHTML = messageHTML;
                        } else {
                            // Append to existing messages
                            chatMessages.innerHTML += messageHTML;
                        }

                        // Increment message count and scroll to bottom
                        currentMessagesCount++;
                        scrollToBottom();
                    } else {
                        console.error('Error sending message:', data.error);
                        alert('Error: ' + data.error);
                    }
                })
                .catch(error => console.error('Error sending message:', error));
        }

        // Function to fetch new messages and check for status updates
        function fetchMessages() {
            <?php if ($ticket_id): ?>
            fetch('get-messages.php?ticket_id=<?php echo $ticket_id; ?>')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Check for status updates
                        const currentTicketStatus = document.getElementById('ticket_status').value;
                        if (data.ticket_status && data.ticket_status !== currentTicketStatus) {
                            updateTicketStatus(data.ticket_status);
                        }

                        // Handle new messages
                        if (data.messages.length > currentMessagesCount) {
                            // Get chat container
                            const chatMessages = document.getElementById('chatMessages');

                            // Check if there are new messages from admin (not from user)
                            let hasNewAdminMessages = false;

                            // Add only new messages
                            for (let i = currentMessagesCount; i < data.messages.length; i++) {
                                const message = data.messages[i];
                                const messageHTML = formatMessage(message);

                                // Check if chat is empty (has the "No messages yet" placeholder)
                                const chatEmpty = chatMessages.querySelector('.chat-empty');
                                if (chatEmpty && i === 0) {
                                    // Replace the empty state with the first message
                                    chatMessages.innerHTML = messageHTML;
                                } else {
                                    // Append to existing messages
                                    chatMessages.innerHTML += messageHTML;
                                }

                                // Check if this is an admin message (not from user)
                                if (message.sender_type === 'admin') {
                                    hasNewAdminMessages = true;
                                }
                            }

                            // Play notification sound and show browser notification for new admin messages
                            if (hasNewAdminMessages && window.chatNotificationSystem) {
                                // Initialize notification system if not already done
                                if (!window.chatNotificationSystem.isInitialized) {
                                    window.chatNotificationSystem.initialize();
                                }

                                const message = data.messages[currentMessagesCount].message || 'New message';

                                // Notify with the first new message
                                window.chatNotificationSystem.notify(
                                    message.substring(0, 100) + (message.length > 100 ? '...' : ''), {
                                        title: 'New message from Support Team',
                                        sender: 'Support Team'
                                    }
                                );
                            }

                            // Update count and scroll to bottom
                            currentMessagesCount = data.messages.length;
                            scrollToBottom();
                        }
                    }
                })
                .catch(error => console.error('Error checking for new messages:', error));

            // Also check for notifications on other tickets
            fetch('../support-ticket/check-notifications')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update notification badges
                        const totalUnread = data.total_unread;
                        const backButtonNotification = document.getElementById('back-button-notification');
                        const userChatBadge = document.getElementById('user-chat-badge');

                        if (backButtonNotification) {
                            if (totalUnread > 0) {
                                backButtonNotification.textContent = totalUnread >= 10 ? '9+' : totalUnread;
                                backButtonNotification.style.display = 'block';
                            } else {
                                backButtonNotification.style.display = 'none';
                            }
                        }

                        if (userChatBadge) {
                            if (totalUnread > 0) {
                                userChatBadge.textContent = totalUnread >= 10 ? '9+' : totalUnread;
                                userChatBadge.style.display = 'block';
                            } else {
                                userChatBadge.style.display = 'none';
                            }
                        }
                    }
                })
                .catch(error => console.error('Error checking notifications:', error));
            <?php endif; ?>
        }

        // Function to check for new notifications (auto-update ticket list badges)
        function checkNotifications() {
            fetch('check-notifications.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update the back button notification
                        const backBtnBadge = document.getElementById('back-button-notification');
                        if (backBtnBadge) {
                            if (data.total_unread > 0) {
                                backBtnBadge.style.display = 'inline-flex';
                                backBtnBadge.textContent = data.total_unread >= 10 ? '9+' : data.total_unread;
                            } else {
                                backBtnBadge.style.display = 'none';
                            }
                        }

                        // Update individual ticket notifications
                        data.tickets.forEach(ticket => {
                            let ticketBadge = document.getElementById('notification-ticket-' + ticket.id);
                            if (!ticketBadge && ticket.unread_count > 0) {
                                // Only create badge if there is a notification to show
                                const ticketLink = document.querySelector(
                                    'a[href="chat-support.php?ticket_id=' + ticket.id + '"]');
                                if (ticketLink) {
                                    const badgeDiv = ticketLink.querySelector('div:last-child');
                                    ticketBadge = document.createElement('span');
                                    ticketBadge.id = 'notification-ticket-' + ticket.id;
                                    ticketBadge.className = 'notification-badge';
                                    badgeDiv.appendChild(ticketBadge);
                                }
                            }
                            if (ticketBadge) {
                                if (ticket.unread_count > 0) {
                                    ticketBadge.style.display = 'inline-flex';
                                    ticketBadge.textContent = ticket.unread_count >= 10 ? '9+' : ticket
                                        .unread_count;
                                } else {
                                    // Remove badge from DOM if no notification
                                    ticketBadge.remove();
                                }
                            }
                        });
                    }
                })
                .catch(error => console.error('Error checking notifications:', error));
        }

        // Call on page load
        window.onload = function() {
            scrollToBottom();

            // Initialize notification system
            if (window.chatNotificationSystem) {
                window.chatNotificationSystem.initialize();

                // Request notification permission when page loads
                setTimeout(() => {
                    window.chatNotificationSystem.requestPermission();
                }, 2000);
            }

            // Set up auto-refresh for messages
            <?php if ($ticket_id): ?>
            setInterval(fetchMessages, 3000);
            <?php else: ?>
            setInterval(checkNotifications, 3000);
            checkNotifications();
            <?php endif; ?>
        };
        </script>
</body>

</html>