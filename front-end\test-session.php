<?php
session_start();

// Auto-detect environment for file paths
$is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
$base_path = $is_localhost ? '/helloit' : '';

echo "<h2>Session Test</h2>";

echo "<h3>Session Information:</h3>";
echo "<p><strong>Session ID:</strong> " . session_id() . "</p>";
echo "<p><strong>Session Status:</strong> " . (session_status() === PHP_SESSION_ACTIVE ? 'Active' : 'Inactive') . "</p>";

echo "<h3>Session Variables:</h3>";
if (!empty($_SESSION)) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Key</th><th>Value</th></tr>";
    foreach ($_SESSION as $key => $value) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($key) . "</td>";
        echo "<td>" . htmlspecialchars(is_array($value) ? json_encode($value) : $value) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>❌ No session variables found</p>";
}

echo "<h3>Login Status:</h3>";
if (isset($_SESSION['user_id']) && isset($_SESSION['username'])) {
    echo "<p style='color: green;'>✅ User is logged in</p>";
    echo "<p><strong>User ID:</strong> " . $_SESSION['user_id'] . "</p>";
    echo "<p><strong>Username:</strong> " . $_SESSION['username'] . "</p>";
} else {
    echo "<p style='color: red;'>❌ User is NOT logged in</p>";
    echo "<p>Missing session variables:</p>";
    echo "<ul>";
    if (!isset($_SESSION['user_id'])) echo "<li>user_id</li>";
    if (!isset($_SESSION['username'])) echo "<li>username</li>";
    echo "</ul>";
}

echo "<h3>Test Links:</h3>";
echo "<p><a href='pre-checkout.php'>Go to Pre-Checkout</a></p>";
echo "<p><a href='sign-in.php'>Go to Sign-In</a></p>";
echo "<p><a href='create-pre-checkout-payment.php?payment_method=pm_test123'>Test Payment Processing</a></p>";

echo "<h3>Test Payment Processing:</h3>";
echo "<button onclick='testPaymentProcessing()'>Test Payment Processing</button>";
echo "<div id='result'></div>";

?>

<script>
function testPaymentProcessing() {
    const basePath = '<?php echo $base_path; ?>';
    const testUrl = basePath + '/front-end/create-pre-checkout-payment.php?payment_method=pm_test123';
    
    document.getElementById('result').innerHTML = '<p>Testing: ' + testUrl + '</p>';
    
    // Try to fetch the URL to see what happens
    fetch(testUrl)
        .then(response => {
            document.getElementById('result').innerHTML += '<p>Response status: ' + response.status + '</p>';
            document.getElementById('result').innerHTML += '<p>Response URL: ' + response.url + '</p>';
            return response.text();
        })
        .then(text => {
            document.getElementById('result').innerHTML += '<p>Response length: ' + text.length + ' characters</p>';
            if (text.includes('sign-in')) {
                document.getElementById('result').innerHTML += '<p style="color: red;">❌ Redirected to sign-in page</p>';
            } else if (text.includes('cart')) {
                document.getElementById('result').innerHTML += '<p style="color: orange;">⚠️ Redirected to cart page</p>';
            } else {
                document.getElementById('result').innerHTML += '<p style="color: green;">✅ Different response</p>';
            }
        })
        .catch(error => {
            document.getElementById('result').innerHTML += '<p style="color: red;">Error: ' + error + '</p>';
        });
}
</script>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
button { background: #007bff; color: white; padding: 10px 15px; border: none; border-radius: 4px; cursor: pointer; }
</style>
